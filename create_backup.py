#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建项目备份
"""

import os
import time
import zipfile
from pathlib import Path

def create_project_backup():
    """创建项目备份"""
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    backup_name = f"project_backup_{timestamp}.zip"
    
    print(f"📦 创建项目备份: {backup_name}")
    
    try:
        with zipfile.ZipFile(backup_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 备份重要目录和文件
            items_to_backup = [
                'src',
                'config', 
                '.kiro',
                'doc',
                'main.py',
                'requirements.txt',
                'assets',
                'examples',
                'tests',
                'sound_library',
                'ffmpeg'
            ]
            
            total_files = 0
            
            for item in items_to_backup:
                if os.path.exists(item):
                    if os.path.isfile(item):
                        zipf.write(item)
                        total_files += 1
                        print(f"   ✅ 添加文件: {item}")
                    elif os.path.isdir(item):
                        for root, dirs, files in os.walk(item):
                            # 跳过不需要的目录
                            dirs[:] = [d for d in dirs if not d.startswith('.') and 
                                     d not in {'__pycache__', 'node_modules'}]
                            
                            for file in files:
                                # 跳过不需要的文件
                                if file.endswith(('.pyc', '.pyo', '.log')):
                                    continue
                                    
                                file_path = os.path.join(root, file)
                                zipf.write(file_path)
                                total_files += 1
                        
                        print(f"   ✅ 添加目录: {item}")
                else:
                    print(f"   ⚠️ 不存在: {item}")
        
        backup_size = os.path.getsize(backup_name) / (1024 * 1024)
        print(f"\n🎉 备份创建成功!")
        print(f"   - 文件名: {backup_name}")
        print(f"   - 文件数: {total_files}")
        print(f"   - 大小: {backup_size:.2f} MB")
        
        return backup_name
        
    except Exception as e:
        print(f"❌ 备份创建失败: {e}")
        return None

if __name__ == "__main__":
    create_project_backup()
