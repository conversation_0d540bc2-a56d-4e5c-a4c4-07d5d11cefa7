# 视频时长同步优化说明

## 问题描述

在最后视频合成阶段，原来的逻辑是以每个镜头的固定时长来合成视频，导致配音未读完就结束了。需要调整逻辑，使视频时长以配音时长为准，如果视频时长不够则循环播放至配音时长。

## 解决方案

### 1. 视频生成阶段优化

**文件**: `src/gui/video_generation_tab.py`

**修改内容**: 优化 `_validate_duration` 方法，根据配音时长智能选择视频时长：

```python
def _validate_duration(self, duration):
    """验证并调整视频时长到最接近的支持时长"""
    supported_durations = [5, 10]  # CogVideoX-Flash只支持5秒和10秒

    # 如果有目标时长（通常来自配音时长），智能选择最合适的视频时长
    if duration > 0:
        if duration <= 5:
            # 配音时长5秒以内，使用5秒视频
            adjusted_duration = 5
        elif duration <= 10:
            # 配音时长5-10秒，使用10秒视频
            adjusted_duration = 10
        else:
            # 配音时长超过10秒，使用10秒视频（后续通过循环播放匹配）
            adjusted_duration = 10
    else:
        # 没有目标时长，使用用户选择的时长
        adjusted_duration = user_selected_duration

    return adjusted_duration
```

**优化效果**:
- 配音时长 ≤ 5秒：生成5秒视频
- 配音时长 5-10秒：生成10秒视频
- 配音时长 > 10秒：生成10秒视频，后续通过循环播放匹配

### 2. 视频合成阶段优化

**文件**: `src/processors/video_composer.py`

**现有逻辑**: 已经实现了完善的视频循环播放机制：

```python
# 使用FFmpeg创建同步的视频音频片段，支持视频循环播放
cmd = [
    self.ffmpeg_path,
    "-stream_loop", "-1",  # 无限循环视频
    "-i", video_path,
    "-i", audio_path,
    "-t", str(audio_duration),  # 设置时长为音频时长
    "-c:v", "libx264",
    "-c:a", "aac",
    "-filter:a", "volume=3.0",
    "-map", "0:v:0",  # 使用视频流（循环）
    "-map", "1:a:0",  # 使用音频流
    "-shortest",  # 以最短流为准（音频）
    "-y",
    synced_video
]
```

**关键参数说明**:
- `-stream_loop -1`: 无限循环视频流
- `-t str(audio_duration)`: 设置输出时长为音频时长
- `-shortest`: 以最短流为准（这里是音频流）

### 3. 多片段处理优化

**文件**: `src/gui/video_generation_tab.py`

**现有逻辑**: 已经实现了超长配音的智能分割：

```python
def _check_voice_duration_match(self, scene_data):
    """检查配音时长是否需要多个图像"""
    voice_duration = scene_data.get('voice_duration', 0.0)
    max_segment_duration = 10.0  # CogVideoX-Flash最大支持10秒
    
    # 计算需要的图像数量
    required_images = max(1, int(voice_duration / max_segment_duration) + 
                         (1 if voice_duration % max_segment_duration > 0 else 0))
    
    # 计算每个片段的时长
    segment_durations = []
    remaining_duration = voice_duration
    
    for i in range(required_images):
        if remaining_duration > max_segment_duration:
            segment_durations.append(max_segment_duration)
            remaining_duration -= max_segment_duration
        else:
            segment_durations.append(remaining_duration)
            break
    
    return required_images, segment_durations
```

## 工作流程

### 单片段场景（配音时长 ≤ 10秒）

1. **视频生成**: 根据配音时长选择5秒或10秒视频
2. **视频合成**: 通过FFmpeg循环播放或截取匹配配音时长

### 多片段场景（配音时长 > 10秒）

1. **智能分割**: 将长配音分割为多个≤10秒的片段
2. **批量生成**: 为每个片段生成对应时长的视频
3. **视频合成**: 连接所有片段，确保总时长匹配配音时长

## 测试验证

通过测试验证了以下场景：

| 配音时长 | 视频时长选择 | 处理方式 |
|---------|-------------|----------|
| 3.5秒   | 5秒         | 视频截取至3.5秒 |
| 5.0秒   | 5秒         | 完美匹配 |
| 7.8秒   | 10秒        | 视频截取至7.8秒 |
| 10.0秒  | 10秒        | 完美匹配 |
| 15.2秒  | 10秒        | 视频循环播放至15.2秒 |
| 25.6秒  | 多片段      | 分割为3个片段：10s+10s+5.6s |

## 优化效果

1. **✅ 时长同步**: 最终视频时长与配音时长完全匹配
2. **✅ 智能选择**: 根据配音时长自动选择最合适的视频时长
3. **✅ 循环播放**: 短视频自动循环播放匹配长配音
4. **✅ 多片段处理**: 超长配音自动分割处理
5. **✅ 无缝衔接**: 保持视频内容的连续性和流畅性

## 问题修复

### 🔧 音频时长获取问题修复

**问题**: FFmpeg获取音频时长时出现编码错误：
```
UnicodeDecodeError: 'gbk' codec can't decode byte 0xab in position 1570
'NoneType' object has no attribute 'split'
```

**解决方案**:

1. **多编码支持**: 修复FFmpeg输出解码问题
```python
# 尝试不同的编码方式解码stderr
for encoding in ['utf-8', 'gbk', 'cp1252', 'latin1']:
    try:
        stderr_text = result.stderr.decode(encoding)
        break
    except UnicodeDecodeError:
        continue
```

2. **多方法获取**: 添加多种音频时长获取方法
```python
# 方法1：使用mutagen（最可靠）
from mutagen import File
audio_file = File(audio_path)
duration = float(audio_file.info.length)

# 方法2：使用FFmpeg（处理编码问题）
# 方法3：文件大小估算（备用方案）
```

### 🔧 视频合成界面时长显示修复

**问题**: 视频合成界面显示默认5秒时长，没有正确获取配音时长

**解决方案**: 修改`load_video_segments`方法，优先使用配音时长：

```python
# 方法1：从音频文件获取实际时长
if audio_path and os.path.exists(audio_path):
    audio_duration = self.get_audio_duration(audio_path)
    if audio_duration > 0:
        duration = audio_duration

# 方法2：从项目数据中的配音信息获取
voice_segments = project_data.get('voice_generation', {}).get('voice_segments', [])
for voice_seg in voice_segments:
    if voice_seg.get('shot_id') == shot_id:
        voice_duration = voice_seg.get('duration', 0.0)
        if voice_duration > 0:
            duration = voice_duration
```

## 技术要点

- 使用FFmpeg的`-stream_loop -1`参数实现视频无限循环
- 通过`-t`参数精确控制输出时长
- 智能分割算法确保每个片段时长不超过引擎限制
- 保持音频音量和视频质量的一致性
- 多编码支持解决FFmpeg输出解析问题
- 多方法音频时长获取确保可靠性
