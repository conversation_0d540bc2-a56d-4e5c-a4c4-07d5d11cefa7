# AI配音功能使用指南

## 功能概述

AI配音功能为AI视频生成器添加了完整的语音合成能力，支持多种配音引擎，可以为分镜脚本自动生成高质量的配音。

## 支持的配音引擎

### 1. Edge-TTS (微软) - 免费推荐 ⭐
- **特点**: 完全免费，质量优秀，支持多语言
- **优势**: 无需API密钥，开箱即用
- **支持语言**: 中文、英文等多种语言
- **音色**: 云希、晓晓、云扬、晓伊等

### 2. CosyVoice (阿里) - 本地部署
- **特点**: 开源本地TTS，高质量中文语音
- **优势**: 完全本地化，无网络依赖
- **要求**: 需要下载并配置CosyVoice模型
- **适用**: 对隐私要求高的场景

### 3. TTSMaker - 在线服务
- **特点**: 在线TTS服务，有免费额度
- **要求**: 需要注册并获取API Key
- **支持**: 多种音色和语言

### 4. 科大讯飞 - 中文专家
- **特点**: 中文TTS专家，自然语音
- **要求**: 需要App ID、API Key、API Secret
- **优势**: 中文语音效果优秀

### 5. ElevenLabs - 高端付费
- **特点**: 最高质量的TTS服务
- **要求**: 付费API服务
- **优势**: 语音质量最佳，情感表达丰富

## 使用流程

### 第一步：配置配音引擎

1. 打开主程序，进入 **设置** 标签页
2. 选择 **🎵 AI配音** 子标签
3. 根据需要配置各个引擎：

#### Edge-TTS配置（推荐新手）
- 无需配置，直接可用
- 选择喜欢的音色和语速

#### 其他引擎配置
- 输入相应的API密钥
- 测试连接确保配置正确

### 第二步：生成配音

1. 进入 **🎵 AI配音生成** 标签页
2. 点击 **📋 从分镜脚本加载** 自动提取配音文本
3. 在右侧选择配音引擎和音色
4. 调整语速等参数
5. 点击 **🎵 批量生成配音** 开始生成

### 第三步：管理音频

- 生成的音频会自动保存到项目目录
- 可以预览播放生成的音频
- 支持导出和管理音频文件

## 文件组织结构

```
项目目录/
├── audio/                 # 音频文件目录
│   ├── edge_tts/         # Edge-TTS生成的音频
│   ├── cosyvoice/        # CosyVoice生成的音频
│   ├── ttsmaker/         # TTSMaker生成的音频
│   ├── xunfei/           # 科大讯飞生成的音频
│   └── elevenlabs/       # ElevenLabs生成的音频
├── subtitles/            # 字幕文件目录
└── project.json          # 项目数据（包含配音信息）
```

## 高级功能

### 1. 文本编辑
- 支持手动编辑配音文本
- 可以调整文本内容和分段

### 2. 批量处理
- 支持批量生成所有配音
- 支持选择性生成部分配音

### 3. 音频管理
- 自动按引擎分类存储
- 支持音频文件导出
- 提供存储空间统计

### 4. 项目集成
- 配音数据自动保存到项目
- 支持项目加载时恢复配音状态
- 与分镜脚本无缝集成

## 常见问题

### Q: Edge-TTS无法使用怎么办？
A: 确保已安装edge-tts包：`pip install edge-tts`

### Q: 如何获取其他引擎的API密钥？
A: 
- TTSMaker: 访问官网注册账号
- 科大讯飞: 在讯飞开放平台申请
- ElevenLabs: 在官网注册并购买服务

### Q: CosyVoice如何配置？
A: 
1. 下载CosyVoice模型
2. 在设置中指定模型路径
3. 确保inference.py脚本可用

### Q: 生成的音频质量不满意怎么办？
A: 
1. 尝试不同的音色
2. 调整语速和音调参数
3. 使用更高质量的引擎（如ElevenLabs）

### Q: 音频文件太大怎么办？
A: 
1. 使用音频管理功能清理不需要的文件
2. 选择压缩率更高的音频格式
3. 定期导出并清理项目音频

## 技术特性

### 1. 异步处理
- 使用异步技术，不阻塞界面
- 支持进度显示和取消操作

### 2. 错误处理
- 完善的错误检测和重试机制
- 详细的错误信息提示

### 3. 数据持久化
- 所有配音数据保存在project.json
- 音频文件按项目组织管理

### 4. 界面友好
- 现代化UI设计
- 直观的操作流程
- 丰富的状态反馈

## 开发扩展

### 添加新的TTS引擎
1. 继承`TTSEngineBase`基类
2. 实现必要的方法
3. 在`TTSEngineManager`中注册
4. 添加对应的UI配置界面

### 自定义音频处理
1. 修改`AudioFileManager`类
2. 添加音频后处理功能
3. 扩展音频格式支持

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 支持5种主流TTS引擎
- ✅ 完整的配音工作流程
- ✅ 现代化UI界面
- ✅ 项目数据集成
- ✅ 音频文件管理
- ✅ 异步处理和错误处理

## 技术支持

如果在使用过程中遇到问题，请：
1. 检查配音引擎的配置是否正确
2. 确认网络连接正常（在线引擎）
3. 查看日志文件获取详细错误信息
4. 参考本文档的常见问题部分

---

**注意**: 使用付费TTS服务时请注意API调用费用，建议先使用免费的Edge-TTS进行测试。
