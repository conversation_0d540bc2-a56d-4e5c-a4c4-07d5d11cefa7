import logging
import os
import sys
import time
from logging.handlers import RotatingFileHandler
import json
from collections import defaultdict
from typing import Dict # 新增导入

# 获取项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
LOG_DIR = os.path.join(PROJECT_ROOT, 'logs')
SYSTEM_LOG_FILE = os.path.join(LOG_DIR, 'system.log')
ERROR_LOG_FILE = os.path.join(LOG_DIR, 'error.log')
CONFIG_FILE = os.path.join(PROJECT_ROOT, 'config', 'log_config.json')

class Logger:
    def __init__(self, name='AIVideoLogger'):
        self.logger = logging.getLogger(name)
        self.logger.handlers.clear() # 清除现有的处理器，避免重复
        self.error_counts = defaultdict(int)
        self.last_log_time = defaultdict(float)
        self.config = self._load_config()
        self._setup_handlers()

    def _load_config(self):
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {
            "console_level": "INFO",
            "file_level": "INFO",
            "error_file_level": "ERROR",
            "enable_console": True,
            "enable_file": True,
            "enable_error_file": True,
            "suppress_repeated_logs": True,
            "suppress_threshold": 10,
            "suppress_interval": 60,
            "log_format": "[%(asctime)s] [%(levelname)s] [%(name)s] [%(module)s:%(lineno)d] [%(funcName)s] %(message)s",
            "date_format": "%Y-%m-%d %H:%M:%S"
        }

    def _setup_handlers(self):
        log_format = self.config.get('log_format', "[%(asctime)s] [%(levelname)s] [%(name)s] [%(module)s:%(lineno)d] [%(funcName)s] %(message)s")
        date_format = self.config.get('date_format', '%Y-%m-%d %H:%M:%S')
        formatter = logging.Formatter(log_format, datefmt=date_format)

        # 确保日志目录存在
        os.makedirs(LOG_DIR, exist_ok=True)

        # 文件处理器
        if self.config.get('enable_file', True):
            fh = RotatingFileHandler(
                SYSTEM_LOG_FILE,
                maxBytes=10*1024*1024,
                backupCount=5,
                encoding='utf-8'
            )
            fh.setLevel(getattr(logging, self.config.get('file_level', 'INFO').upper()))
            fh.setFormatter(formatter)
            self.logger.addHandler(fh)

        # 错误文件处理器
        if self.config.get('enable_error_file', True):
            err_fh = RotatingFileHandler(
                ERROR_LOG_FILE,
                maxBytes=5*1024*1024,
                backupCount=3,
                encoding='utf-8'
            )
            err_fh.setLevel(getattr(logging, self.config.get('error_file_level', 'ERROR').upper()))
            err_fh.setFormatter(formatter)
            self.logger.addHandler(err_fh)

        # 控制台处理器
        if self.config.get('enable_console', True):
            ch = logging.StreamHandler(sys.stdout)
            ch.setLevel(getattr(logging, self.config.get('console_level', 'INFO').upper()))
            console_format = '[%(asctime)s] [%(levelname)s] %(message)s'
            console_formatter = logging.Formatter(console_format, datefmt='%H:%M:%S')
            ch.setFormatter(console_formatter)
            self.logger.addHandler(ch)

    def _should_suppress(self, msg: str) -> bool:
        if not self.config.get('suppress_repeated_logs', False):
            return False

        current_time = time.time()
        if current_time - self.last_log_time[msg] < self.config.get('suppress_interval', 60):
            self.error_counts[msg] += 1
            if self.error_counts[msg] > self.config.get('suppress_threshold', 10):
                return True
        else:
            self.error_counts[msg] = 0
        self.last_log_time[msg] = current_time
        return False

    def get_logger(self):
        return self.logger

    def debug(self, msg):
        if not self._should_suppress(msg): self.logger.debug(msg)

    def info(self, msg):
        if not self._should_suppress(msg): self.logger.info(msg)

    def warning(self, msg):
        if not self._should_suppress(msg): self.logger.warning(msg)

    def error(self, msg):
        self.error_counts[msg] += 1 # 错误总是计数
        if not self._should_suppress(msg): self.logger.error(msg)

    def critical(self, msg):
        self.error_counts[msg] += 1 # 错误总是计数
        if not self._should_suppress(msg): self.logger.critical(msg)

    def exception(self, msg):
        self.error_counts[msg] += 1 # 错误总是计数
        if not self._should_suppress(msg): self.logger.exception(msg)

    def flush(self):
        for handler in self.logger.handlers:
            if hasattr(handler, 'flush'):
                handler.flush()

    def get_error_statistics(self) -> Dict[str, int]:
        """获取错误统计信息"""
        return dict(self.error_counts)


class LoggerWrapper:
    """日志包装器，提供兼容的接口"""

    def __init__(self, logger_instance):
        self._logger_instance = logger_instance
        self._logger = logger_instance.logger

    def debug(self, msg):
        return self._logger_instance.debug(msg)

    def info(self, msg):
        return self._logger_instance.info(msg)

    def warning(self, msg):
        return self._logger_instance.warning(msg)

    def error(self, msg):
        return self._logger_instance.error(msg)

    def critical(self, msg):
        return self._logger_instance.critical(msg)

    def exception(self, msg):
        return self._logger_instance.exception(msg)

    def flush(self):
        return self._logger_instance.flush()

    def get_error_statistics(self) -> Dict[str, int]:
        return self._logger_instance.get_error_statistics()

    def __getattr__(self, name):
        return getattr(self._logger, name)


_logger_instance = Logger()
logger = LoggerWrapper(_logger_instance)
