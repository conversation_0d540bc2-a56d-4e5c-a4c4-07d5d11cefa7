# -*- coding: utf-8 -*-
"""
项目内容分析器
负责从当前项目中提取相关内容作为LLM输入。
"""

from dataclasses import dataclass, field
from typing import List, Optional

from src.utils.logger import logger
from src.core.singleton_manager import get_singleton_service
from src.utils.project_manager import StoryboardProjectManager


@dataclass
class ProjectContent:
    """项目内容数据模型"""
    title: str = ""
    summary: str = ""
    original_text: str = ""
    world_building: Optional[str] = None
    characters: List[str] = field(default_factory=list)
    themes: List[str] = field(default_factory=list)
    genre: str = ""
    target_audience: str = ""
    key_elements: List[str] = field(default_factory=list)

    def is_empty(self) -> bool:
        """检查项目内容是否为空"""
        return not (self.title or self.summary or self.original_text)


class ProjectContentAnalyzer:
    """
    负责从当前项目中提取相关内容作为LLM输入。
    """
    def __init__(self):
        # 使用单例管理器获取项目管理器的实例
        self.project_manager: StoryboardProjectManager = get_singleton_service('project_manager')
        logger.info("项目内容分析器初始化完成")

    def analyze_current_project(self) -> ProjectContent:
        """
        分析当前激活的项目，提取内容。
        这是一个简化版本，主要提取标题、原始文本和简单的摘要。
        """
        logger.info("开始分析当前项目内容...")
        project = self.project_manager.get_current_project()

        if not project or not project.get('project_name'):
            logger.warning("没有当前激活的项目，无法进行分析")
            return ProjectContent()

        # 提取核心内容
        title = project.get('project_name', '未命名项目')
        original_text = project.get('original_text', '')
        summary = ""

        # 尝试从分镜中提取摘要信息
        storyboard = project.get('storyboard')
        if storyboard and isinstance(storyboard, dict):
            summary = storyboard.get('summary', '')
            # 如果摘要为空，可以尝试从前几个镜头的描述中生成一个简单的摘要
            if not summary:
                shots = storyboard.get('shots', [])
                if shots and isinstance(shots, list):
                    summary_parts = [shot.get('scene_description', '') for shot in shots[:3]]
                    summary = " ".join(filter(None, summary_parts))

        content = ProjectContent(
            title=title,
            summary=summary.strip(),
            original_text=original_text.strip(),
            # 其他字段（如角色、主题等）的提取将在后续任务中实现
        )
        
        logger.info(f"项目内容分析完成: 标题='{content.title}'")
        return content


# --- 单例访问 --- #
_analyzer_instance = None

def get_project_content_analyzer() -> ProjectContentAnalyzer:
    """获取项目内容分析器的单例"""
    global _analyzer_instance
    if _analyzer_instance is None:
        _analyzer_instance = ProjectContentAnalyzer()
    return _analyzer_instance
