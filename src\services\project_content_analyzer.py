# -*- coding: utf-8 -*-
"""
项目内容分析器
负责从当前项目中提取相关内容作为LLM输入。
"""

from dataclasses import dataclass, field
from typing import List, Optional

from src.utils.logger import logger
from src.core.singleton_manager import get_singleton_service
from src.utils.project_manager import StoryboardProjectManager


@dataclass
class ProjectContent:
    """项目内容数据模型"""
    title: str = ""
    summary: str = ""
    original_text: str = ""
    world_building: Optional[str] = None
    characters: List[str] = field(default_factory=list)
    themes: List[str] = field(default_factory=list)
    genre: str = ""
    target_audience: str = ""
    key_elements: List[str] = field(default_factory=list)

    def is_empty(self) -> bool:
        """检查项目内容是否为空"""
        return not (self.title or self.summary or self.original_text)


class ProjectContentAnalyzer:
    """
    负责从当前项目中提取相关内容作为LLM输入。
    """
    def __init__(self):
        # 使用单例管理器获取项目管理器的实例
        self.project_manager: StoryboardProjectManager = get_singleton_service('project_manager')
        logger.info("项目内容分析器初始化完成")

    def analyze_current_project(self) -> ProjectContent:
        """
        分析当前激活的项目，提取内容。
        增强版本，支持从多种数据结构中提取原始文本和世界观圣经内容。
        """
        logger.info("开始分析当前项目内容...")
        project = self.project_manager.get_current_project()

        if not project or not project.get('project_name'):
            logger.warning("没有当前激活的项目，无法进行分析")
            return ProjectContent()

        # 提取核心内容
        title = project.get('project_name', '未命名项目')

        # 🔧 修复：使用多种方法提取原始文本
        original_text = self._extract_original_text(project)

        # 🔧 修复：提取世界观圣经内容
        world_building = self._extract_world_bible(project)

        # 提取摘要信息
        summary = self._extract_summary(project)

        content = ProjectContent(
            title=title,
            summary=summary.strip(),
            original_text=original_text.strip(),
            world_building=world_building.strip() if world_building else None,
            # 其他字段（如角色、主题等）的提取将在后续任务中实现
        )

        logger.info(f"项目内容分析完成: 标题='{content.title}', 原始文本长度={len(content.original_text)}, 世界观长度={len(content.world_building or '')}")

        # 如果内容为空，记录详细的调试信息
        if content.is_empty():
            logger.warning("项目内容为空，无法生成内容")
            logger.debug(f"项目数据键: {list(project.keys())}")
            if 'five_stage_storyboard' in project:
                five_stage = project['five_stage_storyboard']
                logger.debug(f"五阶段分镜数据键: {list(five_stage.keys()) if isinstance(five_stage, dict) else type(five_stage)}")

        return content

    def _extract_original_text(self, project: dict) -> str:
        """
        从项目数据中提取原始文本
        支持多种数据结构格式
        """
        # 方法1：优先从project.json根级别的article_text字段获取
        article_text = project.get('article_text', '')
        if article_text:
            logger.info(f"从project.json根级别article_text字段提取原文，长度: {len(article_text)}")
            return article_text

        # 方法2：从five_stage_storyboard根级别提取
        if 'five_stage_storyboard' in project:
            five_stage = project['five_stage_storyboard']
            if isinstance(five_stage, dict):
                article_text = five_stage.get('article_text', '')
                if article_text:
                    logger.info(f"从five_stage_storyboard根级别提取原文，长度: {len(article_text)}")
                    return article_text

        # 方法3：从根级别的original_text和rewritten_text字段获取
        for field in ['rewritten_text', 'original_text']:
            text = project.get(field, '')
            if text:
                logger.info(f"从根级别{field}字段提取原文，长度: {len(text)}")
                return text

        # 方法4：从five_stage_storyboard的各个阶段中提取
        if 'five_stage_storyboard' in project:
            five_stage = project['five_stage_storyboard']
            if isinstance(five_stage, dict):
                stage_data = five_stage.get('stage_data', {})
                if isinstance(stage_data, dict):
                    for stage_key in ['3', '4', '5', '1', '2']:  # 检查各个阶段
                        if stage_key in stage_data:
                            stage_info = stage_data[stage_key]
                            if isinstance(stage_info, dict):
                                article_text = stage_info.get('article_text', '')
                                if article_text:
                                    logger.info(f"从five_stage_storyboard.stage_data.{stage_key}提取原文，长度: {len(article_text)}")
                                    return article_text

        # 方法5：从text_content子字段获取
        text_content = project.get('text_content', {})
        if isinstance(text_content, dict):
            for field in ['rewritten_text', 'original_text']:
                text = text_content.get(field, '')
                if text:
                    logger.info(f"从text_content.{field}提取原文，长度: {len(text)}")
                    return text

        logger.warning("未找到任何原文内容")
        return ""

    def _extract_world_bible(self, project: dict) -> str:
        """
        从项目数据中提取世界观圣经内容
        """
        # 方法1：从五阶段分镜数据中获取
        if 'five_stage_storyboard' in project:
            five_stage = project['five_stage_storyboard']
            if isinstance(five_stage, dict):
                world_bible = five_stage.get('world_bible', '')
                if world_bible:
                    logger.info("从五阶段分镜数据获取世界观圣经内容")
                    return world_bible

                # 从stage_data中获取
                stage_data = five_stage.get('stage_data', {})
                if isinstance(stage_data, dict):
                    stage1_data = stage_data.get('1', {})
                    if isinstance(stage1_data, dict):
                        world_bible = stage1_data.get('world_bible', '')
                        if world_bible:
                            logger.info("从五阶段分镜stage_data.1获取世界观圣经内容")
                            return world_bible

        # 方法2：从根级别获取
        world_bible = project.get('world_bible', '')
        if world_bible:
            logger.info("从项目根级别获取世界观圣经内容")
            return world_bible

        # 方法3：从storyboard中获取
        storyboard = project.get('storyboard', {})
        if isinstance(storyboard, dict):
            world_bible = storyboard.get('world_bible', '')
            if world_bible:
                logger.info("从storyboard获取世界观圣经内容")
                return world_bible

        logger.debug("未找到世界观圣经内容")
        return ""

    def _extract_summary(self, project: dict) -> str:
        """
        从项目数据中提取摘要信息
        """
        # 尝试从分镜中提取摘要信息
        storyboard = project.get('storyboard')
        if storyboard and isinstance(storyboard, dict):
            summary = storyboard.get('summary', '')
            if summary:
                return summary

            # 如果摘要为空，可以尝试从前几个镜头的描述中生成一个简单的摘要
            shots = storyboard.get('shots', [])
            if shots and isinstance(shots, list):
                summary_parts = [shot.get('scene_description', '') for shot in shots[:3]]
                summary = " ".join(filter(None, summary_parts))
                if summary:
                    return summary

        # 从五阶段分镜数据中提取
        if 'five_stage_storyboard' in project:
            five_stage = project['five_stage_storyboard']
            if isinstance(five_stage, dict):
                stage_data = five_stage.get('stage_data', {})
                if isinstance(stage_data, dict):
                    # 从第4阶段（分镜生成）中提取摘要
                    stage4_data = stage_data.get('4', {})
                    if isinstance(stage4_data, dict):
                        storyboard_results = stage4_data.get('storyboard_results', [])
                        if storyboard_results and isinstance(storyboard_results, list):
                            summary_parts = []
                            for result in storyboard_results[:3]:  # 只取前3个
                                if isinstance(result, dict):
                                    scene_info = result.get('scene_info', {})
                                    if isinstance(scene_info, dict):
                                        scene_name = scene_info.get('scene_name', '')
                                        if scene_name:
                                            summary_parts.append(scene_name)
                            if summary_parts:
                                return "、".join(summary_parts)

        return ""


# --- 单例访问 --- #
_analyzer_instance = None

def get_project_content_analyzer() -> ProjectContentAnalyzer:
    """获取项目内容分析器的单例"""
    global _analyzer_instance
    if _analyzer_instance is None:
        _analyzer_instance = ProjectContentAnalyzer()
    return _analyzer_instance
