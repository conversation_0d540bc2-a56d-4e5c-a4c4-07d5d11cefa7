# -*- coding: utf-8 -*-
"""
增强的视频格式转换服务
支持多平台视频格式适配、并发处理、质量检测和验证
"""

import asyncio
import subprocess
import os
import json
import time
import hashlib
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, Optional, Callable, List, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum
from src.utils.logger import logger

class ConversionQuality(Enum):
    """转换质量枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ULTRA = "ultra"


class ConversionStatus(Enum):
    """转换状态枚举"""
    PENDING = "pending"
    ANALYZING = "analyzing"
    CONVERTING = "converting"
    VALIDATING = "validating"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class PlatformSpec:
    """增强的平台视频规格"""
    name: str
    resolution: str  # "1920x1080"
    aspect_ratio: str  # "16:9"
    fps: int
    bitrate: str  # "2M"
    format: str  # "mp4"
    codec: str  # "h264"
    max_duration: int  # 秒，0表示无限制
    max_size: int  # MB

    # 新增字段
    audio_codec: str = "aac"
    audio_bitrate: str = "128k"
    quality_preset: str = "medium"
    upload_chunk_size: int = 5 * 1024 * 1024  # 5MB chunks
    supports_thumbnail: bool = True
    supports_chapters: bool = False
    max_title_length: int = 100
    max_description_length: int = 5000
    max_tags_count: int = 10

    # 质量控制
    min_quality_score: float = 0.7
    target_quality: ConversionQuality = ConversionQuality.MEDIUM


@dataclass
class ConversionTask:
    """转换任务数据模型"""
    id: str
    input_path: str
    output_path: str
    platform: str
    spec: PlatformSpec
    status: ConversionStatus = ConversionStatus.PENDING
    progress: float = 0.0
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None

    # 质量指标
    input_size: Optional[int] = None
    output_size: Optional[int] = None
    quality_score: Optional[float] = None
    compression_ratio: Optional[float] = None

    def get_duration(self) -> Optional[float]:
        """获取任务执行时长"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None


@dataclass
class QualityMetrics:
    """质量评估指标"""
    psnr: Optional[float] = None  # Peak Signal-to-Noise Ratio
    ssim: Optional[float] = None  # Structural Similarity Index
    vmaf: Optional[float] = None  # Video Multimethod Assessment Fusion
    bitrate_efficiency: Optional[float] = None
    size_reduction: Optional[float] = None
    overall_score: float = 0.0

class EnhancedVideoFormatConverter:
    """增强的视频格式转换服务"""

    # 增强的平台规格配置
    PLATFORM_SPECS = {
        'douyin': PlatformSpec(
            name='抖音',
            resolution='1080x1920',
            aspect_ratio='9:16',
            fps=30,
            bitrate='3M',
            format='mp4',
            codec='h264',
            max_duration=60,
            max_size=100,
            audio_codec='aac',
            audio_bitrate='128k',
            quality_preset='fast',
            max_title_length=55,
            max_description_length=2000,
            max_tags_count=10,
            target_quality=ConversionQuality.MEDIUM
        ),
        'kuaishou': PlatformSpec(
            name='快手',
            resolution='1080x1920',
            aspect_ratio='9:16',
            fps=30,
            bitrate='3M',
            format='mp4',
            codec='h264',
            max_duration=57,
            max_size=100,
            audio_codec='aac',
            audio_bitrate='128k',
            quality_preset='fast',
            max_title_length=30,
            max_description_length=1000,
            max_tags_count=8,
            target_quality=ConversionQuality.MEDIUM
        ),
        'bilibili': PlatformSpec(
            name='B站',
            resolution='1920x1080',
            aspect_ratio='16:9',
            fps=60,
            bitrate='8M',
            format='mp4',
            codec='h264',
            max_duration=0,  # 无限制
            max_size=8192,  # 8GB
            audio_codec='aac',
            audio_bitrate='192k',
            quality_preset='medium',
            max_title_length=80,
            max_description_length=5000,
            max_tags_count=12,
            target_quality=ConversionQuality.HIGH
        ),
        'xiaohongshu': PlatformSpec(
            name='小红书',
            resolution='1080x1080',
            aspect_ratio='1:1',
            fps=30,
            bitrate='2M',
            format='mp4',
            codec='h264',
            max_duration=60,
            max_size=100,
            audio_codec='aac',
            audio_bitrate='128k',
            quality_preset='medium',
            max_title_length=20,
            max_description_length=1000,
            max_tags_count=10,
            target_quality=ConversionQuality.MEDIUM
        ),
        'wechat_channels': PlatformSpec(
            name='微信视频号',
            resolution='1080x1920',
            aspect_ratio='9:16',
            fps=30,
            bitrate='2M',
            format='mp4',
            codec='h264',
            max_duration=60,
            max_size=100,
            audio_codec='aac',
            audio_bitrate='128k',
            quality_preset='medium',
            max_title_length=30,
            max_description_length=600,
            max_tags_count=6,
            target_quality=ConversionQuality.MEDIUM
        ),
        'youtube_shorts': PlatformSpec(
            name='YouTube Shorts',
            resolution='1080x1920',
            aspect_ratio='9:16',
            fps=30,
            bitrate='4M',
            format='mp4',
            codec='h264',
            max_duration=60,
            max_size=256,
            audio_codec='aac',
            audio_bitrate='128k',
            quality_preset='medium',
            max_title_length=100,
            max_description_length=5000,
            max_tags_count=15,
            target_quality=ConversionQuality.HIGH
        )
    }

    def __init__(self, ffmpeg_path: str = "ffmpeg", max_workers: int = 3):
        self.ffmpeg_path = ffmpeg_path
        self.temp_dir = Path("temp/video_conversion")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 并发处理配置
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.conversion_queue = asyncio.Queue()
        self.active_tasks: Dict[str, ConversionTask] = {}

        # 质量检测配置
        self.enable_quality_check = True
        self.quality_threshold = 0.7

        # 缓存配置
        self.cache_dir = Path("cache/video_analysis")
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # 检查FFmpeg是否可用
        self._check_ffmpeg()

        logger.info(f"增强视频转换器初始化完成 - 最大并发: {max_workers}")
        
    def _check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(
                [self.ffmpeg_path, '-version'],
                capture_output=True,
                timeout=10
            )
            if result.returncode == 0:
                logger.info("FFmpeg检查通过")
            else:
                logger.warning("FFmpeg可能不可用")
        except Exception as e:
            logger.error(f"FFmpeg检查失败: {e}")

    async def validate_video_quality(self, original_path: str, converted_path: str) -> QualityMetrics:
        """验证视频质量"""
        try:
            metrics = QualityMetrics()

            # 获取文件大小
            original_size = os.path.getsize(original_path)
            converted_size = os.path.getsize(converted_path)

            # 计算压缩比
            metrics.size_reduction = (original_size - converted_size) / original_size

            # 计算比特率效率
            original_info = await self._analyze_video(original_path)
            converted_info = await self._analyze_video(converted_path)

            if original_info.get('bitrate') and converted_info.get('bitrate'):
                metrics.bitrate_efficiency = converted_info['bitrate'] / original_info['bitrate']

            # 简化的质量评分（基于文件大小和比特率）
            size_score = 1.0 - abs(metrics.size_reduction - 0.3)  # 目标压缩30%
            bitrate_score = 1.0 if metrics.bitrate_efficiency and 0.5 <= metrics.bitrate_efficiency <= 1.0 else 0.5

            metrics.overall_score = (size_score + bitrate_score) / 2

            logger.info(f"质量评估完成 - 总分: {metrics.overall_score:.2f}")
            return metrics

        except Exception as e:
            logger.error(f"质量验证失败: {e}")
            return QualityMetrics(overall_score=0.5)  # 默认中等质量

    def _get_video_hash(self, video_path: str) -> str:
        """获取视频文件哈希值用于缓存"""
        try:
            with open(video_path, 'rb') as f:
                # 只读取文件的前1MB来计算哈希，提高性能
                content = f.read(1024 * 1024)
                return hashlib.md5(content).hexdigest()
        except Exception:
            return str(time.time())  # 如果失败，使用时间戳

    async def _get_cached_analysis(self, video_path: str) -> Optional[Dict[str, Any]]:
        """获取缓存的视频分析结果"""
        try:
            video_hash = self._get_video_hash(video_path)
            cache_file = self.cache_dir / f"{video_hash}.json"

            if cache_file.exists():
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cached_data = json.load(f)
                    # 检查缓存是否过期（24小时）
                    if time.time() - cached_data.get('timestamp', 0) < 86400:
                        logger.debug(f"使用缓存的视频分析结果: {video_path}")
                        return cached_data.get('analysis')

            return None
        except Exception as e:
            logger.debug(f"获取缓存分析失败: {e}")
            return None

    async def _cache_analysis(self, video_path: str, analysis: Dict[str, Any]) -> None:
        """缓存视频分析结果"""
        try:
            video_hash = self._get_video_hash(video_path)
            cache_file = self.cache_dir / f"{video_hash}.json"

            cache_data = {
                'timestamp': time.time(),
                'video_path': video_path,
                'analysis': analysis
            }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.debug(f"缓存分析结果失败: {e}")
        
    async def convert_for_platform(self, 
                                 input_path: str,
                                 platform: str,
                                 output_dir: str,
                                 progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """为指定平台转换视频格式"""
        try:
            if platform not in self.PLATFORM_SPECS:
                raise ValueError(f"不支持的平台: {platform}")
                
            spec = self.PLATFORM_SPECS[platform]
            
            if progress_callback:
                progress_callback(0.1, "分析视频信息...")
                
            # 分析输入视频
            video_info = await self._analyze_video(input_path)
            
            if progress_callback:
                progress_callback(0.2, "检查转换需求...")
                
            # 检查是否需要转换
            if self._needs_conversion(video_info, spec):
                output_path = os.path.join(output_dir, f"{platform}_{Path(input_path).stem}.{spec.format}")
                
                if progress_callback:
                    progress_callback(0.3, "生成转换命令...")
                
                # 生成FFmpeg命令
                cmd = self._build_ffmpeg_command(input_path, output_path, spec, video_info)
                
                if progress_callback:
                    progress_callback(0.4, "开始视频转换...")
                
                # 执行转换
                success = await self._execute_conversion(
                    cmd, 
                    lambda p, msg: progress_callback(0.4 + p * 0.5, msg) if progress_callback else None
                )
                
                if success and os.path.exists(output_path):
                    if progress_callback:
                        progress_callback(1.0, "转换完成")
                        
                    return {
                        'success': True,
                        'output_path': output_path,
                        'platform': platform,
                        'original_size': os.path.getsize(input_path),
                        'converted_size': os.path.getsize(output_path),
                        'spec_applied': spec.__dict__
                    }
                else:
                    raise Exception("转换失败或输出文件不存在")
            else:
                # 不需要转换，直接返回原文件
                if progress_callback:
                    progress_callback(1.0, "无需转换")
                    
                return {
                    'success': True,
                    'output_path': input_path,
                    'platform': platform,
                    'conversion_needed': False
                }
                
        except Exception as e:
            logger.error(f"视频转换失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'platform': platform
            }
            
    async def _analyze_video(self, video_path: str) -> Dict[str, Any]:
        """增强的视频信息分析（支持缓存）"""
        try:
            # 尝试从缓存获取
            cached_result = await self._get_cached_analysis(video_path)
            if cached_result:
                return cached_result

            # 尝试使用ffprobe分析视频
            ffprobe_path = self.ffmpeg_path.replace('ffmpeg', 'ffprobe')
            cmd = [
                ffprobe_path,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                video_path
            ]

            result = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                info = json.loads(stdout.decode())

                # 提取视频流信息
                video_stream = None
                audio_stream = None
                for stream in info.get('streams', []):
                    if stream.get('codec_type') == 'video' and not video_stream:
                        video_stream = stream
                    elif stream.get('codec_type') == 'audio' and not audio_stream:
                        audio_stream = stream

                if video_stream:
                    # 安全地计算帧率
                    fps = 30  # 默认帧率
                    try:
                        r_frame_rate = video_stream.get('r_frame_rate', '30/1')
                        if '/' in r_frame_rate:
                            num, den = r_frame_rate.split('/')
                            fps = int(num) / int(den) if int(den) != 0 else 30
                        else:
                            fps = float(r_frame_rate)
                    except:
                        fps = 30

                    analysis_result = {
                        'width': int(video_stream.get('width', 0)),
                        'height': int(video_stream.get('height', 0)),
                        'fps': fps,
                        'codec': video_stream.get('codec_name', ''),
                        'duration': float(info.get('format', {}).get('duration', 0)),
                        'bitrate': int(info.get('format', {}).get('bit_rate', 0)),
                        'size': int(info.get('format', {}).get('size', 0)),
                        # 新增字段
                        'pixel_format': video_stream.get('pix_fmt', ''),
                        'profile': video_stream.get('profile', ''),
                        'level': video_stream.get('level', ''),
                        'has_audio': audio_stream is not None,
                        'audio_codec': audio_stream.get('codec_name', '') if audio_stream else '',
                        'audio_bitrate': int(audio_stream.get('bit_rate', 0)) if audio_stream else 0,
                        'file_path': video_path,
                        'analyzed_at': time.time()
                    }

                    # 缓存分析结果
                    await self._cache_analysis(video_path, analysis_result)
                    return analysis_result

            # 如果ffprobe失败，返回默认值
            default_result = {
                'width': 1920,
                'height': 1080,
                'fps': 30,
                'codec': 'unknown',
                'duration': 0,
                'bitrate': 0,
                'size': os.path.getsize(video_path) if os.path.exists(video_path) else 0,
                'pixel_format': 'unknown',
                'profile': 'unknown',
                'level': 'unknown',
                'has_audio': True,
                'audio_codec': 'unknown',
                'audio_bitrate': 0,
                'file_path': video_path,
                'analyzed_at': time.time()
            }

            return default_result

        except Exception as e:
            logger.error(f"分析视频信息失败: {e}")
            # 返回基本信息
            return {
                'width': 1920,
                'height': 1080,
                'fps': 30,
                'codec': 'unknown',
                'duration': 0,
                'bitrate': 0,
                'size': os.path.getsize(video_path) if os.path.exists(video_path) else 0,
                'pixel_format': 'unknown',
                'profile': 'unknown',
                'level': 'unknown',
                'has_audio': True,
                'audio_codec': 'unknown',
                'audio_bitrate': 0,
                'file_path': video_path,
                'analyzed_at': time.time()
            }
            
    def _needs_conversion(self, video_info: Dict[str, Any], spec: PlatformSpec) -> bool:
        """检查是否需要转换"""
        if not video_info:
            return True
            
        # 检查分辨率
        target_width, target_height = map(int, spec.resolution.split('x'))
        if video_info.get('width') != target_width or video_info.get('height') != target_height:
            return True
            
        # 检查帧率
        if abs(video_info.get('fps', 0) - spec.fps) > 1:
            return True
            
        # 检查编码格式
        if video_info.get('codec', '').lower() != spec.codec.lower():
            return True
            
        return False
        
    def _build_ffmpeg_command(self, input_path: str, output_path: str,
                             spec: PlatformSpec, video_info: Dict) -> List[str]:
        """构建增强的FFmpeg转换命令"""
        cmd = [
            self.ffmpeg_path,
            '-i', input_path,
            '-c:v', spec.codec,
            '-b:v', spec.bitrate,
            '-r', str(spec.fps),
            '-s', spec.resolution,
            '-aspect', spec.aspect_ratio,
            '-c:a', spec.audio_codec,
            '-b:a', spec.audio_bitrate,
            '-movflags', '+faststart',  # 优化网络播放
            '-y',  # 覆盖输出文件
        ]

        # 根据目标质量添加编码参数
        if spec.target_quality == ConversionQuality.LOW:
            cmd.extend(['-preset', 'ultrafast', '-crf', '32'])
        elif spec.target_quality == ConversionQuality.MEDIUM:
            cmd.extend(['-preset', spec.quality_preset, '-crf', '28'])
        elif spec.target_quality == ConversionQuality.HIGH:
            cmd.extend(['-preset', 'slow', '-crf', '23'])
        elif spec.target_quality == ConversionQuality.ULTRA:
            cmd.extend(['-preset', 'veryslow', '-crf', '18'])

        # 根据平台添加特殊参数
        if spec.name == 'B站':
            cmd.extend(['-profile:v', 'high', '-level', '4.1'])
            # B站支持更高质量
            if '-crf' not in cmd:
                cmd.extend(['-crf', '20'])
        elif '抖音' in spec.name or '快手' in spec.name:
            # 短视频平台优化
            cmd.extend(['-profile:v', 'main', '-level', '3.1'])
            cmd.extend(['-g', '60'])  # GOP大小
        elif 'YouTube' in spec.name:
            # YouTube优化
            cmd.extend(['-profile:v', 'high', '-level', '4.0'])
            cmd.extend(['-bf', '2', '-g', '120'])

        # 音频优化
        if video_info.get('has_audio', True):
            # 如果原视频有音频，保持音频
            if spec.audio_codec == 'aac':
                cmd.extend(['-aac_coder', 'twoloop'])
        else:
            # 如果原视频没有音频，添加静音音轨
            cmd.extend(['-f', 'lavfi', '-i', 'anullsrc=channel_layout=stereo:sample_rate=48000'])
            cmd.extend(['-shortest'])

        # 像素格式优化
        cmd.extend(['-pix_fmt', 'yuv420p'])

        # 添加输出路径
        cmd.append(output_path)

        return cmd
        
    async def _execute_conversion(self, cmd: List[str], 
                                progress_callback: Optional[Callable] = None) -> bool:
        """执行转换命令"""
        try:
            logger.info(f"执行转换命令: {' '.join(cmd)}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # 模拟进度更新
            if progress_callback:
                for i in range(10):
                    await asyncio.sleep(0.5)
                    progress_callback(i / 10, f"转换进度: {i*10}%")
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("视频转换成功")
                return True
            else:
                error_msg = stderr.decode() if stderr else "未知错误"
                logger.error(f"视频转换失败: {error_msg}")
                return False
                
        except Exception as e:
            logger.error(f"执行转换命令失败: {e}")
            return False
            
    async def batch_convert(self, tasks: List[Dict[str, Any]], 
                          progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """批量转换视频"""
        results = []
        total_tasks = len(tasks)
        
        for i, task in enumerate(tasks):
            try:
                if progress_callback:
                    progress_callback(i / total_tasks, f"处理任务 {i+1}/{total_tasks}")
                    
                result = await self.convert_for_platform(
                    input_path=task['input_path'],
                    platform=task['platform'],
                    output_dir=task['output_dir'],
                    progress_callback=None  # 不传递内部进度
                )
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"批量转换任务 {i+1} 失败: {e}")
                results.append({
                    'success': False,
                    'error': str(e),
                    'platform': task.get('platform', 'unknown')
                })
                
        if progress_callback:
            progress_callback(1.0, "批量转换完成")
            
        return results

    async def concurrent_convert(self, tasks: List[Dict[str, Any]],
                               progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """并发转换多个视频"""
        try:
            logger.info(f"开始并发转换 {len(tasks)} 个任务")

            # 创建转换任务
            conversion_tasks = []
            for i, task in enumerate(tasks):
                task_id = f"task_{int(time.time())}_{i}"
                conversion_task = ConversionTask(
                    id=task_id,
                    input_path=task['input_path'],
                    output_path=task.get('output_path', ''),
                    platform=task['platform'],
                    spec=self.PLATFORM_SPECS.get(task['platform'])
                )
                conversion_tasks.append(conversion_task)
                self.active_tasks[task_id] = conversion_task

            # 使用信号量控制并发数量
            semaphore = asyncio.Semaphore(self.max_workers)

            async def convert_single_task(conv_task: ConversionTask) -> Dict[str, Any]:
                async with semaphore:
                    try:
                        conv_task.status = ConversionStatus.ANALYZING
                        conv_task.started_at = time.time()

                        # 分析视频
                        video_info = await self._analyze_video(conv_task.input_path)
                        conv_task.input_size = video_info.get('size', 0)

                        # 检查是否需要转换
                        if not self._needs_conversion(video_info, conv_task.spec):
                            conv_task.status = ConversionStatus.COMPLETED
                            conv_task.completed_at = time.time()
                            conv_task.progress = 1.0
                            return {
                                'success': True,
                                'output_path': conv_task.input_path,
                                'platform': conv_task.platform,
                                'conversion_needed': False,
                                'task_id': conv_task.id
                            }

                        # 生成输出路径
                        if not conv_task.output_path:
                            output_dir = Path(conv_task.input_path).parent / "converted"
                            output_dir.mkdir(exist_ok=True)
                            conv_task.output_path = str(output_dir / f"{conv_task.platform}_{Path(conv_task.input_path).stem}.{conv_task.spec.format}")

                        # 执行转换
                        conv_task.status = ConversionStatus.CONVERTING
                        cmd = self._build_ffmpeg_command(conv_task.input_path, conv_task.output_path, conv_task.spec, video_info)

                        success = await self._execute_conversion(
                            cmd,
                            lambda p, msg: self._update_task_progress(conv_task.id, p * 0.8 + 0.1, msg)
                        )

                        if success and os.path.exists(conv_task.output_path):
                            # 质量验证
                            if self.enable_quality_check:
                                conv_task.status = ConversionStatus.VALIDATING
                                self._update_task_progress(conv_task.id, 0.9, "验证质量...")

                                quality_metrics = await self.validate_video_quality(
                                    conv_task.input_path, conv_task.output_path
                                )
                                conv_task.quality_score = quality_metrics.overall_score

                                if quality_metrics.overall_score < self.quality_threshold:
                                    logger.warning(f"质量评分过低: {quality_metrics.overall_score}")

                            conv_task.status = ConversionStatus.COMPLETED
                            conv_task.completed_at = time.time()
                            conv_task.progress = 1.0
                            conv_task.output_size = os.path.getsize(conv_task.output_path)

                            if conv_task.input_size and conv_task.output_size:
                                conv_task.compression_ratio = conv_task.output_size / conv_task.input_size

                            return {
                                'success': True,
                                'output_path': conv_task.output_path,
                                'platform': conv_task.platform,
                                'task_id': conv_task.id,
                                'quality_score': conv_task.quality_score,
                                'compression_ratio': conv_task.compression_ratio,
                                'duration': conv_task.get_duration()
                            }
                        else:
                            raise Exception("转换失败或输出文件不存在")

                    except Exception as e:
                        conv_task.status = ConversionStatus.FAILED
                        conv_task.error_message = str(e)
                        conv_task.completed_at = time.time()
                        logger.error(f"转换任务失败 {conv_task.id}: {e}")
                        return {
                            'success': False,
                            'error': str(e),
                            'platform': conv_task.platform,
                            'task_id': conv_task.id
                        }
                    finally:
                        # 清理活跃任务
                        if conv_task.id in self.active_tasks:
                            del self.active_tasks[conv_task.id]

            # 并发执行所有任务
            results = await asyncio.gather(*[convert_single_task(task) for task in conversion_tasks])

            # 更新总体进度
            if progress_callback:
                progress_callback(1.0, f"并发转换完成 - 成功: {sum(1 for r in results if r['success'])}/{len(results)}")

            logger.info(f"并发转换完成 - 成功: {sum(1 for r in results if r['success'])}/{len(results)}")
            return results

        except Exception as e:
            logger.error(f"并发转换失败: {e}")
            return [{'success': False, 'error': str(e)} for _ in tasks]

    def _update_task_progress(self, task_id: str, progress: float, message: str = "") -> None:
        """更新任务进度"""
        if task_id in self.active_tasks:
            self.active_tasks[task_id].progress = progress
            if message:
                logger.debug(f"任务 {task_id}: {message} ({progress*100:.1f}%)")

    def get_active_tasks(self) -> Dict[str, ConversionTask]:
        """获取活跃的转换任务"""
        return self.active_tasks.copy()

    def get_task_status(self, task_id: str) -> Optional[ConversionTask]:
        """获取指定任务状态"""
        return self.active_tasks.get(task_id)

    def get_platform_specs(self) -> Dict[str, PlatformSpec]:
        """获取所有平台规格"""
        return self.PLATFORM_SPECS.copy()

    def get_supported_platforms(self) -> List[str]:
        """获取支持的平台列表"""
        return list(self.PLATFORM_SPECS.keys())

    async def cleanup(self) -> None:
        """清理资源"""
        try:
            # 关闭线程池
            self.executor.shutdown(wait=True)
            logger.info("视频转换器资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


# 保持向后兼容性
class VideoFormatConverter(EnhancedVideoFormatConverter):
    """向后兼容的视频格式转换器"""
    pass
