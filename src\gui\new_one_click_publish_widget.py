# -*- coding: utf-8 -*-
"""
新的一键发布界面
简洁直观的发布界面，提供清晰的操作流程和状态反馈
"""

import os
from typing import List, Dict, Any

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QTextEdit, QPushButton, QCheckBox,
    QGroupBox, QFileDialog, QMessageBox, QComboBox,
    QProgressBar, QListWidget, QListWidgetItem, QFrame
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QColor

from src.services.new_one_click_publisher import (
    get_new_one_click_publisher, VideoInfo, PublishTask, PublishStatus
)
from src.services.platform_publishers import (
    DouyinPublisher, <PERSON><PERSON>houPublisher, <PERSON><PERSON><PERSON>Publisher, <PERSON><PERSON><PERSON>iPublisher, YouTubePublisher
)
# --- 新增导入 ---
from src.services.content_generation_workflow import get_content_generation_workflow, OptimizedContent, WorkflowProgress
from src.utils.logger import logger


class AIContentWorkerThread(QThread):
    """AI内容生成工作线程"""
    content_generated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(object) # 新增信号，用于发送WorkflowProgress对象

    def __init__(self, platforms: List[str]):
        super().__init__()
        self.platforms = platforms
        self.workflow = get_content_generation_workflow()

    def run(self):
        try:
            import asyncio
            logger.info(f"AI内容生成线程启动，目标平台: {self.platforms}")
            # 在新线程中运行asyncio事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 定义内部进度回调函数
            def internal_progress_callback(progress_obj: WorkflowProgress):
                self.progress_updated.emit(progress_obj)

            results = loop.run_until_complete(
                self.workflow.execute(self.platforms, progress_callback=internal_progress_callback)
            )
            loop.close()
            
            self.content_generated.emit(results)
        except Exception as e:
            logger.error(f"AI内容生成线程异常: {e}")
            self.error_occurred.emit(str(e))


class PublishWorkerThread(QThread):
    """发布工作线程 - 使用Firefox浏览器"""
    progress_updated = pyqtSignal(float, str)
    task_completed = pyqtSignal(dict)

    def __init__(self, task: PublishTask):
        super().__init__()
        self.task = task
    
    def run(self):
        try:
            # 获取发布器
            publisher = get_new_one_click_publisher()
            
            # 注册平台发布器 - 统一使用Firefox
            for platform in self.task.target_platforms:
                if platform == 'douyin':
                    publisher.register_publisher('douyin', DouyinPublisher())
                elif platform == 'kuaishou':
                    publisher.register_publisher('kuaishou', KuaishouPublisher())
                elif platform == 'xiaohongshu':
                    publisher.register_publisher('xiaohongshu', XiaohongshuPublisher())
                elif platform == 'bilibili':
                    publisher.register_publisher('bilibili', BilibiliPublisher())
                elif platform == 'youtube':
                    publisher.register_publisher('youtube', YouTubePublisher())
            
            # 执行发布
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(publisher.publish_task(self.task, self.progress_callback))
            loop.close()

            self.task_completed.emit(result)
            
        except Exception as e:
            logger.error(f"发布工作线程异常: {e}")
            self.task_completed.emit({
                'success': False,
                'error': str(e)
            })
    
    def progress_callback(self, progress: float, message: str):
        """进度回调"""
        self.progress_updated.emit(progress, message)


class NewOneClickPublishWidget(QWidget):
    """新的一键发布界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_task = None
        self.worker_thread = None
        self.ai_worker_thread = None
        self.generated_content: Dict[str, OptimizedContent] = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(12)
        main_layout.setContentsMargins(16, 16, 16, 16)

        # 创建主要内容区域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(16)

        # 上半部分：文件选择和信息编辑（水平布局）
        top_section = QHBoxLayout()
        top_section.setSpacing(16)

        # 左侧：文件选择和信息编辑
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(12)

        self.create_file_section(left_layout)
        self.create_info_section(left_layout)

        # 右侧：发布设置
        right_panel = QWidget()
        right_panel.setMaximumWidth(300)
        right_panel.setMinimumWidth(280)
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(12)

        self.create_settings_section(right_layout)
        self.create_control_section(right_layout)

        top_section.addWidget(left_panel, 2)
        top_section.addWidget(right_panel, 1)

        content_layout.addLayout(top_section)

        # 下半部分：进度和结果显示
        self.create_progress_section(content_layout)
        self.create_result_section(content_layout)

        main_layout.addWidget(content_widget)

        # 🔧 修复：初始化时检查项目状态并更新UI
        self.update_ui_based_on_project_status()

    def create_title_section(self, layout):
        """创建标题区域（已移除，不再显示）"""
        pass
    
    def create_file_section(self, layout):
        """创建文件选择区域"""
        file_group = QGroupBox("📁 视频文件")
        file_group.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        file_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #fafbfc;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                background-color: #fafbfc;
            }
        """)
        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(8)

        file_select_layout = QHBoxLayout()
        file_select_layout.setSpacing(8)

        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("请选择要发布的视频文件...")
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #d1d9e0;
                border-radius: 6px;
                font-size: 12px;
                background-color: white;
                color: #586069;
            }
            QLineEdit:focus {
                border-color: #0366d6;
            }
        """)

        self.select_file_btn = QPushButton("📂 选择")
        self.select_file_btn.clicked.connect(self.select_video_file)
        self.select_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #0366d6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: 500;
                min-width: 80px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0256cc;
            }
            QPushButton:pressed {
                background-color: #024ea4;
            }
        """)

        file_select_layout.addWidget(self.file_path_edit)
        file_select_layout.addWidget(self.select_file_btn)
        file_layout.addLayout(file_select_layout)

        layout.addWidget(file_group)
    
    def create_info_section(self, layout):
        """创建信息编辑区域"""
        info_group = QGroupBox("📝 视频信息")
        info_group.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #fafbfc;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                background-color: #fafbfc;
            }
        """)
        info_layout = QVBoxLayout(info_group)
        info_layout.setSpacing(12)

        # --- AI生成按钮 ---
        ai_section_layout = QHBoxLayout()
        ai_section_layout.setSpacing(10)
        
        self.ai_generate_btn = QPushButton("🤖 AI生成标题和描述")
        self.ai_generate_btn.clicked.connect(self.run_ai_generation)
        self.ai_generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #2c974b;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: 500;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #278742;
            }
            QPushButton:pressed {
                background-color: #227739;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)
        
        # 刷新项目状态按钮
        self.refresh_project_btn = QPushButton("🔄")
        self.refresh_project_btn.setToolTip("刷新项目状态")
        self.refresh_project_btn.clicked.connect(self.update_ui_based_on_project_status)
        self.refresh_project_btn.setStyleSheet("""
            QPushButton {
                background-color: #f6f8fa;
                color: #24292e;
                border: 1px solid #d1d9e0;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 12px;
                min-width: 30px;
                max-width: 30px;
            }
            QPushButton:hover {
                background-color: #f3f4f6;
                border-color: #c9d1d9;
            }
            QPushButton:pressed {
                background-color: #e1e4e8;
            }
        """)

        self.ai_status_label = QLabel("点击按钮，AI将根据项目内容生成信息")
        self.ai_status_label.setStyleSheet("color: #6a737d; font-size: 11px;")

        ai_section_layout.addWidget(self.ai_generate_btn)
        ai_section_layout.addWidget(self.refresh_project_btn)
        ai_section_layout.addWidget(self.ai_status_label)
        ai_section_layout.addStretch()

        # 标题
        title_layout = QVBoxLayout()
        title_layout.setSpacing(4)
        title_label = QLabel("标题 *")
        title_label.setStyleSheet("color: #24292e; font-weight: 500; font-size: 12px;")
        
        self.title_combo = QComboBox()
        self.title_combo.setPlaceholderText("输入或选择一个视频标题...")
        self.title_combo.setEditable(True) # 允许用户编辑
        self.title_combo.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #d1d9e0;
                border-radius: 6px;
                font-size: 12px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #0366d6;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addWidget(self.title_combo)

        # 描述
        desc_layout = QVBoxLayout()
        desc_layout.setSpacing(4)
        desc_label = QLabel("描述")
        desc_label.setStyleSheet("color: #24292e; font-weight: 500; font-size: 12px;")
        self.desc_edit = QTextEdit()
        self.desc_edit.setPlaceholderText("输入视频描述（可选）...")
        self.desc_edit.setMaximumHeight(70)
        self.desc_edit.setStyleSheet("""
            QTextEdit {
                padding: 8px 12px;
                border: 1px solid #d1d9e0;
                border-radius: 6px;
                font-size: 12px;
                background-color: white;
            }
            QTextEdit:focus {
                border-color: #0366d6;
            }
        """)
        desc_layout.addWidget(desc_label)
        desc_layout.addWidget(self.desc_edit)

        # 标签
        tags_layout = QVBoxLayout()
        tags_layout.setSpacing(4)
        tags_label = QLabel("标签 (用空格或逗号分隔)")
        tags_label.setStyleSheet("color: #24292e; font-weight: 500; font-size: 12px;")
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("AI生成的标签将显示在这里...")
        self.tags_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #d1d9e0;
                border-radius: 6px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #0366d6;
            }
        """)
        tags_layout.addWidget(tags_label)
        tags_layout.addWidget(self.tags_edit)

        info_layout.addLayout(ai_section_layout)
        info_layout.addWidget(QFrame()) # 分隔线
        info_layout.addLayout(title_layout)
        info_layout.addLayout(desc_layout)
        info_layout.addLayout(tags_layout)

        layout.addWidget(info_group)
    
    def create_settings_section(self, layout):
        """创建设置区域"""
        # 平台选择
        platform_group = QGroupBox("🎯 目标平台")
        platform_group.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        platform_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #fafbfc;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                background-color: #fafbfc;
            }
        """)
        platform_layout = QVBoxLayout(platform_group)
        platform_layout.setSpacing(6)

        self.platform_checkboxes = {}
        platforms = [
            ('douyin', '📱 抖音'),
            ('kuaishou', '⚡ 快手'),
            ('xiaohongshu', '📖 小红书'),
            ('bilibili', '📺 B站'),
            ('youtube', '🌐 YouTube')
        ]

        for platform_id, platform_name in platforms:
            checkbox = QCheckBox(platform_name)
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 12px;
                    color: #24292e;
                    spacing: 8px;
                }
                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                    border-radius: 3px;
                    border: 1px solid #d1d9e0;
                    background-color: white;
                }
                QCheckBox::indicator:checked {
                    background-color: #0366d6;
                    border-color: #0366d6;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCA0IDYgNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwLjI4NTcgMC44NTcxNDNMLTQuMTQyODYgN0wwLjg1NzE0MyAzLjcxNDI5IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                }
                QCheckBox::indicator:hover {
                    border-color: #0366d6;
                }
            """)
            checkbox.stateChanged.connect(self.on_platform_checkbox_changed) # 连接信号
            self.platform_checkboxes[platform_id] = checkbox
            platform_layout.addWidget(checkbox)

        layout.addWidget(platform_group)

        # 浏览器选择
        browser_group = QGroupBox("🌐 浏览器")
        browser_group.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        browser_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #fafbfc;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                background-color: #fafbfc;
            }
        """)
        browser_layout = QVBoxLayout(browser_group)
        browser_layout.setSpacing(8)

        self.browser_combo = QComboBox()
        self.browser_combo.addItems(["🦊 Firefox (推荐)", "🌐 Chrome"])
        self.browser_combo.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #d1d9e0;
                border-radius: 6px;
                font-size: 12px;
                background-color: white;
                min-height: 16px;
            }
            QComboBox:focus {
                border-color: #0366d6;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNiIgdmlld0JveD0iMCAwIDEwIDYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNSA1TDkgMSIgc3Ryb2tlPSIjNTg2MDY5IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)
        browser_layout.addWidget(self.browser_combo)

        browser_info = QLabel("💡 Firefox无需配置，Chrome需要调试模式")
        browser_info.setStyleSheet("color: #6a737d; font-size: 10px; padding: 4px;")
        browser_layout.addWidget(browser_info)

        layout.addWidget(browser_group)
    
    def create_control_section(self, layout):
        """创建控制区域"""
        control_layout = QVBoxLayout()
        control_layout.setSpacing(8)

        # 主要发布按钮
        self.publish_btn = QPushButton("🚀 开始发布")
        self.publish_btn.clicked.connect(self.start_publish)
        self.publish_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #20c997);
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 13px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #218838, stop:1 #1e7e34);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)

        # 取消按钮
        self.cancel_btn = QPushButton("⏹️ 取消")
        self.cancel_btn.clicked.connect(self.cancel_publish)
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 500;
                font-size: 12px;
                min-height: 16px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)

        control_layout.addWidget(self.publish_btn)
        control_layout.addWidget(self.cancel_btn)
        control_layout.addStretch()

        layout.addLayout(control_layout)
    
    def create_progress_section(self, layout):
        """创建进度区域"""
        progress_group = QGroupBox("📊 发布进度")
        progress_group.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        progress_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #fafbfc;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                background-color: #fafbfc;
            }
        """)
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setSpacing(8)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #d1d9e0;
                border-radius: 6px;
                text-align: center;
                font-weight: 500;
                font-size: 11px;
                color: #24292e;
                background-color: #f6f8fa;
                min-height: 20px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #28a745, stop:1 #20c997);
                border-radius: 5px;
            }
        """)

        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("""
            color: #6a737d;
            padding: 4px 8px;
            font-size: 12px;
            background-color: white;
            border: 1px solid #e1e5e9;
            border-radius: 4px;
        """)

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)

        layout.addWidget(progress_group)
    
    def create_result_section(self, layout):
        """创建结果区域"""
        result_group = QGroupBox("📋 发布结果")
        result_group.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        result_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #fafbfc;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                background-color: #fafbfc;
            }
        """)
        result_layout = QVBoxLayout(result_group)
        result_layout.setSpacing(8)

        self.result_list = QListWidget()
        self.result_list.setMaximumHeight(100)
        self.result_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #d1d9e0;
                border-radius: 6px;
                font-size: 12px;
                background-color: white;
                alternate-background-color: #f6f8fa;
                selection-background-color: #0366d6;
                selection-color: white;
            }
            QListWidget::item {
                padding: 6px 8px;
                border-bottom: 1px solid #eaecef;
            }
            QListWidget::item:last {
                border-bottom: none;
            }
            QListWidget::item:hover {
                background-color: #f1f8ff;
            }
        """)

        result_layout.addWidget(self.result_list)
        layout.addWidget(result_group)
    
    def select_video_file(self):
        """选择视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
    
    def get_selected_platforms(self) -> List[str]:
        """获取选中的平台"""
        selected = []
        for platform_id, checkbox in self.platform_checkboxes.items():
            if checkbox.isChecked():
                selected.append(platform_id)
        return selected

    def _check_project_loaded(self) -> bool:
        """检查项目是否已加载"""
        try:
            from src.core.singleton_manager import get_singleton_service
            project_manager = get_singleton_service('project_manager')

            if not project_manager:
                QMessageBox.warning(self, "提示",
                    "项目管理器未初始化。\n\n"
                    "请确保程序已正确启动。")
                return False

            current_project = project_manager.get_current_project()
            if not current_project:
                QMessageBox.warning(self, "提示",
                    "请先加载一个项目。\n\n"
                    "AI内容生成需要从项目的原文或世界观中提取信息。\n"
                    "请在主界面中打开或创建一个项目后再使用此功能。")
                return False

            # 检查项目是否有可用的内容
            project_name = current_project.get('project_name', '未命名项目')
            has_content = (
                current_project.get('original_text') or
                current_project.get('rewritten_text') or
                current_project.get('article_text') or
                (current_project.get('five_stage_storyboard', {}).get('world_bible')) or
                (current_project.get('five_stage_storyboard', {}).get('article_text'))
            )

            if not has_content:
                QMessageBox.warning(self, "提示",
                    f"项目 '{project_name}' 中没有找到可用的文本内容。\n\n"
                    "AI内容生成需要以下内容之一：\n"
                    "• 原始文本\n"
                    "• 改写文本\n"
                    "• 世界观圣经\n\n"
                    "请先在项目中添加这些内容后再使用此功能。")
                return False

            logger.info(f"项目检查通过: {project_name}")
            return True

        except Exception as e:
            logger.error(f"检查项目加载状态失败: {e}")
            QMessageBox.critical(self, "错误",
                f"检查项目状态时发生错误：{e}\n\n"
                "请重新启动程序或联系技术支持。")
            return False

    def update_ui_based_on_project_status(self):
        """根据项目加载状态更新UI"""
        try:
            from src.core.singleton_manager import get_singleton_service
            project_manager = get_singleton_service('project_manager')

            if not project_manager:
                # 项目管理器未初始化
                if hasattr(self, 'ai_generate_btn'):
                    self.ai_generate_btn.setEnabled(False)
                    self.ai_generate_btn.setToolTip("项目管理器未初始化")
                if hasattr(self, 'ai_status_label'):
                    self.ai_status_label.setText("⚠️ 项目管理器未初始化")
                return

            current_project = project_manager.get_current_project()
            if not current_project:
                # 没有加载项目
                if hasattr(self, 'ai_generate_btn'):
                    self.ai_generate_btn.setEnabled(False)
                    self.ai_generate_btn.setToolTip("请先加载一个项目")
                if hasattr(self, 'ai_status_label'):
                    self.ai_status_label.setText("📂 请先加载项目以使用AI生成功能")
                return

            # 检查项目内容
            project_name = current_project.get('project_name', '未命名项目')
            has_content = (
                current_project.get('original_text') or
                current_project.get('rewritten_text') or
                current_project.get('article_text') or
                (current_project.get('five_stage_storyboard', {}).get('world_bible')) or
                (current_project.get('five_stage_storyboard', {}).get('article_text'))
            )

            if not has_content:
                # 项目没有可用内容
                if hasattr(self, 'ai_generate_btn'):
                    self.ai_generate_btn.setEnabled(False)
                    self.ai_generate_btn.setToolTip(f"项目 '{project_name}' 中没有可用的文本内容")
                if hasattr(self, 'ai_status_label'):
                    self.ai_status_label.setText(f"⚠️ 项目 '{project_name}' 缺少文本内容")
                return

            # 项目状态正常
            if hasattr(self, 'ai_generate_btn'):
                self.ai_generate_btn.setEnabled(True)
                self.ai_generate_btn.setToolTip("点击生成AI内容")
            if hasattr(self, 'ai_status_label'):
                self.ai_status_label.setText(f"✅ 项目 '{project_name}' 已加载，可以使用AI生成")

        except Exception as e:
            logger.error(f"更新UI状态失败: {e}")
            if hasattr(self, 'ai_generate_btn'):
                self.ai_generate_btn.setEnabled(False)
                self.ai_generate_btn.setToolTip("检查项目状态时发生错误")
            if hasattr(self, 'ai_status_label'):
                self.ai_status_label.setText("❌ 检查项目状态时发生错误")

    def run_ai_generation(self):
        """运行AI内容生成"""
        # 🔧 修复：检查项目是否已加载
        if not self._check_project_loaded():
            return

        platforms = self.get_selected_platforms()
        if not platforms:
            QMessageBox.warning(self, "提示", "请先在右侧选择至少一个目标平台。")
            return

        self.ai_generate_btn.setEnabled(False)
        self.ai_status_label.setText("🤖 正在调用AI，请稍候...")

        self.ai_worker_thread = AIContentWorkerThread(platforms)
        self.ai_worker_thread.content_generated.connect(self.on_ai_content_generated)
        self.ai_worker_thread.error_occurred.connect(self.on_ai_generation_failed)
        self.ai_worker_thread.progress_updated.connect(self.on_ai_generation_progress) # 连接进度信号
        self.ai_worker_thread.start()

    def on_ai_generation_progress(self, progress_obj: WorkflowProgress):
        """AI内容生成进度更新回调"""
        message = progress_obj.message
        if progress_obj.platform:
            message = f"[{progress_obj.platform}] {message}"
        self.ai_status_label.setText(f"🤖 {message}")

    def on_ai_content_generated(self, results: Dict[str, OptimizedContent]):
        """AI内容生成完成后的回调"""
        self.ai_generate_btn.setEnabled(True)
        self.ai_status_label.setText("AI生成完成！请选择标题并检查描述。")
        self.generated_content = results
        
        # 默认显示第一个选中平台的内容
        self._display_generated_content_for_platform()

    def on_ai_generation_failed(self, error_message: str):
        """AI内容生成失败后的回调"""
        self.ai_generate_btn.setEnabled(True)
        self.ai_status_label.setText("AI生成失败，请重试。")
        QMessageBox.critical(self, "AI生成错误", f"调用AI时发生错误: {error_message}")

    def on_platform_checkbox_changed(self):
        """平台复选框状态改变时，更新显示内容"""
        self._display_generated_content_for_platform()

    def _display_generated_content_for_platform(self):
        """根据当前选中的平台显示AI生成的内容"""
        selected_platforms = self.get_selected_platforms()
        if not selected_platforms or not self.generated_content:
            self.title_combo.clear()
            self.title_combo.setPlaceholderText("输入或选择一个视频标题...")
            self.desc_edit.clear()
            self.tags_edit.clear()
            return

        # 优先显示第一个选中的平台的内容
        current_platform_id = selected_platforms[0]
        if current_platform_id in self.generated_content:
            content = self.generated_content[current_platform_id]
            
            # 填充标题下拉框
            self.title_combo.clear()
            if content.titles:
                self.title_combo.addItems(content.titles)
                self.title_combo.setCurrentText(content.titles[0]) # 默认选中第一个
            else:
                self.title_combo.setPlaceholderText("未生成标题，请手动输入...")

            # 填充描述
            self.desc_edit.setText(content.description)

            # 填充标签
            self.tags_edit.setText(", ".join(content.tags))

            # 显示警告信息
            if content.warnings:
                QMessageBox.warning(self, "内容优化警告", "\n".join(content.warnings))
        else:
            # 如果当前选中的平台没有生成内容，则清空
            self.title_combo.clear()
            self.title_combo.setPlaceholderText("未生成标题，请手动输入...")
            self.desc_edit.clear()
            self.tags_edit.clear()
            logger.warning(f"当前选中的平台 '{current_platform_id}' 没有AI生成内容。")

    def validate_input(self) -> bool:
        """验证输入"""
        # 检查视频文件
        video_path = self.file_path_edit.text().strip()
        if not video_path:
            QMessageBox.warning(self, "警告", "请选择视频文件")
            return False
        
        if not os.path.exists(video_path):
            QMessageBox.warning(self, "警告", "视频文件不存在")
            return False
        
        # 检查标题
        title = self.title_combo.currentText().strip()
        if not title:
            QMessageBox.warning(self, "警告", "请输入或选择一个视频标题")
            return False
        
        # 检查平台
        platforms = self.get_selected_platforms()
        if not platforms:
            QMessageBox.warning(self, "警告", "请至少选择一个发布平台")
            return False
        
        return True
    
    def start_publish(self):
        """开始发布"""
        if not self.validate_input():
            return
        
        # 创建视频信息
        video_info = VideoInfo(
            file_path=self.file_path_edit.text().strip(),
            title=self.title_combo.currentText().strip(),
            description=self.desc_edit.toPlainText().strip(),
            tags=[tag.strip() for tag in self.tags_edit.text().split(',') if tag.strip()] # 获取标签
        )
        
        # 创建发布任务
        publisher = get_new_one_click_publisher()
        self.current_task = publisher.create_task(video_info, self.get_selected_platforms())
        
        # 更新界面状态
        self.publish_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.result_list.clear()
        
        # 启动工作线程 - 使用Firefox浏览器
        self.worker_thread = PublishWorkerThread(self.current_task)
        self.worker_thread.progress_updated.connect(self.update_progress)
        self.worker_thread.task_completed.connect(self.on_publish_completed)
        self.worker_thread.start()
    
    def cancel_publish(self):
        """取消发布"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.terminate()
            self.worker_thread.wait()
        
        publisher = get_new_one_click_publisher()
        publisher.cancel_task()
        
        self.publish_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("发布已取消")
    
    def update_progress(self, progress: float, message: str):
        """更新进度"""
        self.progress_bar.setValue(int(progress * 100))
        self.status_label.setText(message)
    
    def on_publish_completed(self, result: Dict[str, Any]):
        """发布完成处理"""
        self.publish_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        if result['success']:
            self.status_label.setText(result['message'])
            
            # 显示结果
            for platform, platform_result in result.get('results', {}).items():
                if platform_result['success']:
                    self.add_result_item(f"✅ {platform}", platform_result['message'])
                else:
                    self.add_result_item(f"❌ {platform}", platform_result['error'])
            
            QMessageBox.information(self, "发布完成", result['message'])
        else:
            self.status_label.setText(f"发布失败: {result.get('error', '未知错误')}")
            self.add_result_item("❌ 发布失败", result.get('error', '未知错误'))
            QMessageBox.critical(self, "发布失败", result.get('error', '未知错误'))
    
    def add_result_item(self, title: str, message: str):
        """添加结果项"""
        item = QListWidgetItem(f"{title}: {message}")

        # 设置更美观的颜色
        if "✅" in title:
            item.setBackground(QColor("#d4edda"))  # 淡绿色背景
            item.setForeground(QColor("#155724"))  # 深绿色文字
        elif "❌" in title:
            item.setBackground(QColor("#f8d7da"))  # 淡红色背景
            item.setForeground(QColor("#721c24"))  # 深红色文字
        else:
            item.setBackground(QColor("#d1ecf1"))  # 淡蓝色背景
            item.setForeground(QColor("#0c5460"))  # 深蓝色文字

        self.result_list.addItem(item)
    
    def get_widget_title(self) -> str:
        """获取组件标题"""
        return "新一键发布"
