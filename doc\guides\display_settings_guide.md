# 显示设置和DPI适配指南

## 概述

本软件已集成了完整的DPI适配和显示设置功能，可以自动适配不同分辨率和DPI设置的显示器，确保界面和文字在各种屏幕上都能清晰可读。

## 功能特性

### 1. 自动DPI检测和适配
- 自动检测系统DPI设置
- 根据DPI自动调整字体大小和界面元素
- 支持高DPI显示器（4K、5K等）
- 兼容Windows DPI缩放设置

### 2. 快速字体调整
- 工具栏内置字体调整器
- 支持快捷键快速调整字体大小
- 实时预览字体效果
- 多种预设字体大小

### 3. 显示设置对话框
- 完整的显示设置界面
- 字体族选择
- 自定义缩放因子
- 窗口大小自适应设置

## 使用方法

### 快速字体调整

#### 工具栏字体调整器
在主界面工具栏右侧，您可以找到字体调整器：
- **A-** 按钮：减小字体
- **滑块**：拖动调整字体大小
- **A+** 按钮：增大字体
- **数字标签**：显示当前字体大小
- **重置** 按钮：恢复默认字体大小

#### 快捷键
- `Ctrl + +`：增大字体
- `Ctrl + -`：减小字体
- `Ctrl + 0`：重置字体大小

#### 菜单选项
在 **视图** → **字体大小** 菜单中：
- 快速调整选项（增大、减小、重置）
- 预设字体大小（8pt - 20pt）

### 显示设置对话框

#### 打开方式
- 菜单：**视图** → **显示设置**
- 快捷键：`Ctrl + D`

#### 设置选项

**字体设置**
- **字体大小**：使用滑块或输入框调整（8-20pt）
- **字体族**：选择系统字体（推荐微软雅黑）

**DPI缩放设置**
- **自动DPI缩放**：启用后自动根据系统DPI调整
- **缩放因子**：手动设置缩放比例（50%-300%）

**窗口设置**
- **自动调整窗口大小**：根据屏幕分辨率自动调整窗口大小

**预览区域**
- 实时预览字体效果
- 查看调整后的显示效果

## 技术细节

### DPI检测机制
```
标准DPI: 96 (100%缩放)
高DPI阈值:
- 120 DPI (125%缩放) → 字体11pt
- 144 DPI (150%缩放) → 字体12pt
- 192 DPI (200%缩放) → 字体14pt
```

### 字体大小映射
- **极小**: 8pt - 适合高分辨率小屏幕
- **小**: 9pt - 默认最小字体
- **正常**: 10pt - 标准字体大小
- **中等**: 11pt - 125% DPI推荐
- **大**: 12pt - 150% DPI推荐
- **较大**: 14pt - 200% DPI推荐
- **特大**: 16pt - 高DPI大屏幕
- **超大**: 18pt - 视力辅助
- **巨大**: 20pt - 最大字体

### 自适应窗口大小
程序会根据屏幕分辨率自动计算合适的窗口大小：
- 基础大小：1200x800
- 自适应规则：屏幕可用区域的80%
- 最小限制：确保界面元素可见

## 配置文件

显示设置保存在 `config/display_settings.json`：

```json
{
    "display": {
        "auto_dpi_scaling": true,
        "custom_scale_factor": 1.0,
        "font_family": "Microsoft YaHei UI",
        "base_font_size": 10,
        "window_size": {
            "width": 1200,
            "height": 800,
            "auto_resize": true
        }
    }
}
```

## 常见问题

### Q: 字体太小看不清怎么办？
A: 
1. 使用工具栏的字体调整器快速增大字体
2. 按 `Ctrl + +` 快捷键增大字体
3. 打开显示设置对话框进行详细调整

### Q: 界面元素显示不完整？
A:
1. 检查是否启用了自动DPI缩放
2. 调整缩放因子到合适的值
3. 启用自动调整窗口大小功能

### Q: 设置不生效怎么办？
A:
1. 确保点击了"应用"按钮
2. 某些设置需要重启程序生效
3. 检查配置文件是否正确保存

### Q: 如何恢复默认设置？
A:
1. 在显示设置对话框中点击"重置"
2. 删除 `config/display_settings.json` 文件
3. 重启程序将使用默认设置

## 兼容性

### 支持的操作系统
- Windows 10/11 (推荐)
- Windows 7/8.1 (基本支持)

### 支持的DPI设置
- 100% (96 DPI)
- 125% (120 DPI)
- 150% (144 DPI)
- 175% (168 DPI)
- 200% (192 DPI)
- 250% (240 DPI)
- 300% (288 DPI)

### 支持的分辨率
- 1920x1080 (Full HD)
- 2560x1440 (2K)
- 3840x2160 (4K)
- 5120x2880 (5K)
- 超宽屏显示器

## 开发者信息

如需自定义DPI适配行为，可以修改以下文件：
- `src/utils/dpi_adapter.py` - DPI适配核心逻辑
- `src/gui/display_settings_dialog.py` - 设置界面
- `src/gui/quick_font_adjuster.py` - 快速字体调整器

更多技术细节请参考源代码注释。
