# -*- coding: utf-8 -*-
"""
LLM服务统一接口
定义了与大语言模型服务交互的标准化接口。
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any

from src.utils.logger import logger
from src.utils.config_manager import ConfigManager # 新增导入
# 假设您现有的LLM API封装在 src.models.llm_api 中
# 如果不是，请修改为正确的路径
from src.models.llm_api import LLMApi


class LLMServiceInterface(ABC):
    """LLM服务统一接口（抽象基类）"""

    @abstractmethod
    async def generate_text(
        self, 
        prompt: str, 
        max_tokens: int = 2048,
        temperature: float = 0.7
    ) -> str:
        """生成文本内容"""
        pass

    @abstractmethod
    async def generate_structured_content(
        self, 
        prompt: str, 
        schema: Dict
    ) -> Dict:
        """生成结构化内容"""
        pass


class GeminiLLMService(LLMServiceInterface):
    """
    LLM服务接口的具体实现，适配您项目中现有的LLMApi。
    """
    def __init__(self):
        config_manager = ConfigManager()
        llm_config = config_manager.get_llm_config()
        
        # 从配置中获取LLM参数
        api_type = llm_config.get('api_type', 'google')
        api_key = llm_config.get('api_key', '')
        api_url = llm_config.get('api_url', '')

        # 直接实例化LLMApi
        self.llm_api = LLMApi(api_type=api_type, api_key=api_key, api_url=api_url)
        logger.info("LLM服务接口 (Gemini) 初始化完成")

    async def generate_text(
        self, 
        prompt: str, 
        max_tokens: int = 2048, 
        temperature: float = 0.7
    ) -> str:
        """使用现有的LLM Api生成文本"""
        try:
            logger.info(f"向LLM发送文本生成请求... (temperature={temperature})")
            # 调用您现有的方法，这里假设是 call_llm
            # 您可能需要根据实际情况调整这里的调用方式
            response = await self.llm_api.call_llm(prompt, temperature=temperature)
            
            # 假设返回的数据结构是 {"text": "..."}
            if isinstance(response, dict) and 'text' in response:
                logger.info("LLM文本生成成功")
                return response['text']
            else:
                logger.error(f"LLM返回了非预期的格式: {response}")
                raise ValueError("LLM did not return the expected format.")

        except Exception as e:
            logger.error(f"LLM文本生成失败: {e}")
            # 在这里可以加入重试逻辑
            raise

    async def generate_structured_content(
        self,
        prompt: str,
        schema: Dict
    ) -> Dict:
        """
        生成结构化内容。
        注意：这通常需要LLM本身支持函数调用或JSON模式。
        如果您的LLM不支持，我们将在这里用prompt工程来模拟它。
        """
        logger.info("向LLM发送结构化内容生成请求...")
        
        # 构建一个特殊的prompt，要求LLM返回JSON格式
        structured_prompt = (
            f"{prompt}\n\n"
            f"请严格按照以下JSON格式返回你的答案，不要包含任何额外的解释或标记。\n"
            f"JSON格式定义如下:\n"
            f"```json\n{schema}```"
        )
        
        try:
            # 复用文本生成方法
            raw_response = await self.generate_text(structured_prompt)
            
            # 解析返回的JSON
            import json
            try:
                # 尝试从Markdown代码块中提取JSON
                json_match = asyncio.run(self._extract_json_from_markdown(raw_response))
                if json_match:
                    logger.info("LLM结构化内容生成成功")
                    return json.loads(json_match)
                else:
                    # 如果没有找到Markdown，尝试直接解析
                    logger.info("LLM结构化内容生成成功 (直接解析)")
                    return json.loads(raw_response)
            except json.JSONDecodeError:
                logger.error(f"无法将LLM的输出解析为JSON: {raw_response}")
                raise ValueError("LLM did not return valid JSON.")

        except Exception as e:
            logger.error(f"LLM结构化内容生成失败: {e}")
            raise

    async def _extract_json_from_markdown(self, text: str) -> str:
        """从Markdown格式的文本中提取JSON字符串"""
        import re
        match = re.search(r"```json\n(.*?)\n```", text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return ""


# --- 单例访问 --- #
_llm_service_instance = None

def get_llm_service() -> LLMServiceInterface:
    """获取LLM服务接口的单例"""
    global _llm_service_instance
    if _llm_service_instance is None:
        _llm_service_instance = GeminiLLMService()
    return _llm_service_instance
