# 显示设置使用指南

## 🎯 功能概述

显示设置功能已成功集成到AI视频生成器中，为用户提供了完整的显示适配和字体调整功能。无论您使用什么分辨率的显示器，都能获得最佳的使用体验。

## 🚀 如何使用显示设置

### 方法一：通过设置页面（推荐）

1. **启动程序**
   ```bash
   python main.py
   ```

2. **进入设置页面**
   - 点击左侧导航栏的 `⚙️ 系统设置`

3. **切换到显示设置**
   - 在设置页面顶部，点击 `🖥️ 显示设置` 标签页

4. **使用显示设置功能**
   - **快速字体调整**: 使用工具栏中的字体调整器
   - **查看显示信息**: 查看当前DPI、缩放因子、屏幕分辨率等信息
   - **高级设置**: 点击 `🔧 高级显示设置` 按钮打开完整设置对话框
   - **重置设置**: 点击 `🔄 重置为默认` 按钮恢复默认设置

### 方法二：通过快速字体调整器

在设置页面的显示设置标签页中，您会看到快速字体调整器：

```
字体大小: [A-] [====●====] [A+] [12] [重置]
```

- **A-**: 减小字体大小
- **滑块**: 拖动调整字体大小
- **A+**: 增大字体大小
- **数字**: 显示当前字体大小
- **重置**: 重置为推荐字体大小

## 📊 显示信息说明

在显示设置页面，您可以看到以下信息：

| 项目 | 说明 | 示例值 |
|------|------|--------|
| 当前DPI | 系统检测到的DPI值 | 96 |
| 缩放因子 | 相对于标准DPI的缩放比例 | 1.0 |
| 屏幕分辨率 | 当前屏幕的像素分辨率 | 1920x1080 |
| 推荐字体大小 | 基于DPI自动推荐的字体大小 | 10pt |

## 🎨 高级显示设置对话框

点击 `🔧 高级显示设置` 按钮可以打开完整的显示设置对话框，包含：

### 字体设置标签页
- **字体大小调整**: 滑块 + 数值输入
- **字体大小预设**: 9个预设选项（极小到巨大）
- **字体族选择**: 系统字体 + 常用字体选择

### DPI缩放标签页
- **DPI信息显示**: 当前DPI、类别、缩放因子
- **自动DPI缩放**: 启用/禁用自动缩放
- **自定义缩放因子**: 手动设置缩放比例（50%-300%）

### 窗口设置标签页
- **屏幕信息**: 分辨率、推荐窗口大小
- **自动调整窗口大小**: 根据屏幕分辨率自动调整

### 预览区域
- **实时预览**: 查看字体和缩放设置的实际效果

## 🔧 自动适配功能

### DPI自动检测
程序启动时会自动检测您的显示器DPI设置：

| DPI范围 | 缩放比例 | 推荐字体大小 | 适用场景 |
|---------|----------|-------------|----------|
| 96 DPI | 100% | 10pt | 标准显示器 |
| 120 DPI | 125% | 11pt | 中等DPI显示器 |
| 144 DPI | 150% | 12pt | 高DPI显示器 |
| 168 DPI | 175% | 13pt | 高分辨率显示器 |
| 192 DPI | 200% | 14pt | 4K显示器 |
| 240+ DPI | 250%+ | 16-18pt | 超高分辨率显示器 |

### 窗口大小自适应
- **自动计算**: 根据屏幕分辨率自动计算最佳窗口大小
- **合理限制**: 窗口大小限制在屏幕的80%-95%之间
- **最小保障**: 确保窗口不小于800x600

## 💾 设置保存

所有显示设置都会自动保存到配置文件：

- **配置文件位置**: `~/.ai_video_generator/display_settings.json`
- **自动保存**: 设置更改时立即保存
- **启动加载**: 程序启动时自动加载保存的设置

## 🔄 重置功能

如果设置出现问题，可以使用重置功能：

1. **快速重置**: 在显示设置页面点击 `🔄 重置为默认`
2. **确认重置**: 系统会询问确认
3. **自动应用**: 重置后自动应用默认设置

## 🎯 使用技巧

### 1. 针对不同显示器的建议

**标准显示器 (1920x1080, 100%缩放)**
- 推荐字体大小: 10-11pt
- 窗口大小: 1200x800

**高DPI显示器 (2560x1440, 125%缩放)**
- 推荐字体大小: 11-12pt
- 窗口大小: 1400x900

**4K显示器 (3840x2160, 150%缩放)**
- 推荐字体大小: 12-14pt
- 窗口大小: 1600x1000

### 2. 字体选择建议

**中文界面推荐字体**:
- Microsoft YaHei UI (默认)
- Microsoft YaHei
- SimHei

**英文界面推荐字体**:
- Segoe UI
- Arial
- Tahoma

### 3. 性能优化建议

- **启用自动DPI缩放**: 让系统自动适配，减少手动调整
- **使用推荐字体大小**: 基于DPI的智能推荐通常是最佳选择
- **启用自动窗口调整**: 根据屏幕大小自动调整窗口

## ❓ 常见问题

### Q: 字体显示模糊怎么办？
A: 
1. 检查系统DPI设置是否正确
2. 尝试启用自动DPI缩放
3. 手动调整字体大小到合适值

### Q: 窗口太大或太小怎么办？
A:
1. 启用自动调整窗口大小功能
2. 手动调整窗口大小后会自动保存
3. 使用重置功能恢复默认设置

### Q: 设置没有保存怎么办？
A:
1. 检查配置文件目录是否有写入权限
2. 尝试手动保存设置
3. 重启程序重新设置

### Q: 高DPI显示器显示异常怎么办？
A:
1. 确保系统DPI设置正确
2. 启用自动DPI缩放功能
3. 尝试不同的缩放因子设置

## 🎉 总结

显示设置功能为AI视频生成器提供了完整的显示适配能力，让您在任何显示器上都能获得最佳的使用体验。通过智能的DPI检测、灵活的字体调整和自适应的窗口大小，确保程序在各种显示环境下都能完美运行。

如果您在使用过程中遇到任何问题，请参考本指南的常见问题部分，或联系技术支持获取帮助。
