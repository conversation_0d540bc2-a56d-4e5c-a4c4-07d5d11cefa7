# 用户指南 📖

本目录包含AI视频生成器的详细用户指南，帮助您掌握各项功能的使用方法。

## 📋 指南列表

### 🎬 核心功能
- [**五阶段分镜系统使用指南**](五阶段分镜系统使用指南.md) - 详细介绍五阶段分镜系统的使用方法
- [**图像生成使用指南**](图像生成使用指南.md) - 图像生成功能的完整指南
- [**语音合成使用指南**](语音合成使用指南.md) - 语音合成和配音功能指南
- [**视频生成使用指南**](视频生成使用指南.md) - 视频生成和合成功能指南

### 🎨 高级功能
- [**一致性控制使用指南**](一致性控制使用指南.md) - 角色和场景一致性控制
- [**智能角色检测使用指南**](智能角色检测使用指南.md) - 智能角色检测系统
- [**场景描述增强使用指南**](场景描述增强使用指南.md) - 场景描述增强器

### 🔄 工作流程
- [**传统文本驱动工作流**](传统文本驱动工作流.md) - 基于文本的传统工作流程
- [**配音驱动工作流**](配音驱动工作流.md) - 基于配音的新型工作流程
- [**项目管理指南**](项目管理指南.md) - 项目创建、保存和管理

## 🎯 推荐学习路径

### 新手入门
1. 先阅读 [快速开始指南](../STARTUP_GUIDE.md)
2. 学习 [五阶段分镜系统](五阶段分镜系统使用指南.md)
3. 掌握 [图像生成功能](图像生成使用指南.md)
4. 了解 [项目管理](项目管理指南.md)

### 进阶使用
1. 学习 [配音驱动工作流](配音驱动工作流.md)
2. 掌握 [一致性控制](一致性控制使用指南.md)
3. 使用 [智能角色检测](智能角色检测使用指南.md)
4. 优化 [场景描述](场景描述增强使用指南.md)

### 专业制作
1. 熟练运用所有功能模块
2. 自定义工作流程
3. 批量处理和自动化
4. 质量控制和优化

## 💡 使用技巧

### 提高效率
- 使用快捷键和批量操作
- 合理配置AI服务参数
- 利用模板和预设
- 定期保存项目进度

### 提升质量
- 精心准备输入文本
- 合理使用一致性控制
- 多次迭代优化结果
- 注意细节和整体效果

## 🔗 相关资源

- [技术文档](../technical/README.md) - 深入了解技术实现
- [故障排除](../troubleshooting/README.md) - 解决使用问题
- [功能特性](../features/README.md) - 了解最新功能

---

**提示**: 建议按照推荐学习路径逐步学习，遇到问题可查看故障排除指南。
