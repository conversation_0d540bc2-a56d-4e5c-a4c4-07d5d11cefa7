# -*- coding: utf-8 -*-
"""
平台内容优化器
根据不同平台的规则和限制，对AI生成的内容进行微调和验证。
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any

from src.utils.logger import logger
from src.services.ai_content_generator import GeneratedContent


@dataclass
class OptimizedContent:
    """优化后的内容模型"""
    titles: List[str] = field(default_factory=list)
    description: str = ""
    tags: List[str] = field(default_factory=list)
    platform: str = ""
    warnings: List[str] = field(default_factory=list) # 优化过程中产生的警告


class PlatformContentOptimizer:
    """
    针对不同平台优化生成的内容。
    """
    def __init__(self):
        self.platform_rules = self._load_platform_rules()
        logger.info("平台内容优化器初始化完成")

    def _load_platform_rules(self) -> Dict[str, Dict[str, Any]]:
        """加载或定义各平台的内容规则"""
        # 这些规则也应该移到配置文件中
        return {
            "douyin": {
                "title_max_length": 30,
                "description_max_length": 1000,
                "max_tags": 10,
                "tag_prefix": "# "
            },
            "bilibili": {
                "title_max_length": 80,
                "description_max_length": 2500,
                "max_tags": 20,
                "tag_prefix": ""
            },
            "xiaohongshu": {
                "title_max_length": 20,
                "description_max_length": 1000,
                "max_tags": 10,
                "tag_prefix": "#"
            },
            "kuaishou": {
                "title_max_length": 30,
                "description_max_length": 1000,
                "max_tags": 8,
                "tag_prefix": "#"
            },
            "default": {
                "title_max_length": 100,
                "description_max_length": 5000,
                "max_tags": 50,
                "tag_prefix": "# "
            }
        }

    def optimize_for_platform(
        self, 
        content: GeneratedContent, 
        platform: str
    ) -> OptimizedContent:
        """为特定平台优化内容"""
        logger.info(f"开始为平台 '{platform}' 优化内容...")
        rules = self.platform_rules.get(platform.lower(), self.platform_rules['default'])
        warnings = []

        # 1. 优化标题
        optimized_titles = []
        for title in content.titles:
            if len(title) > rules['title_max_length']:
                warnings.append(f"标题 '{title[:10]}...' 过长，已被截断。")
                optimized_titles.append(title[:rules['title_max_length']])
            else:
                optimized_titles.append(title)

        # 2. 优化描述
        optimized_desc = content.description
        if len(optimized_desc) > rules['description_max_length']:
            warnings.append("描述过长，已被截断。")
            optimized_desc = optimized_desc[:rules['description_max_length']]

        # 3. 优化标签
        optimized_tags = content.tags[:rules['max_tags']]
        if len(content.tags) > rules['max_tags']:
            warnings.append(f"标签数量过多，已截取前 {rules['max_tags']} 个。")
        
        # 添加标签前缀
        tag_prefix = rules.get('tag_prefix', '')
        if tag_prefix:
            optimized_tags = [f"{tag_prefix}{tag}" for tag in optimized_tags]

        optimized_content = OptimizedContent(
            titles=optimized_titles,
            description=optimized_desc,
            tags=optimized_tags,
            platform=platform,
            warnings=warnings
        )

        if warnings:
            logger.warning(f"为平台 '{platform}' 优化内容时产生警告: {warnings}")
        else:
            logger.info(f"平台 '{platform}' 内容优化完成，无警告。")

        return optimized_content


# --- 单例访问 --- #
_optimizer_instance = None

def get_platform_content_optimizer() -> PlatformContentOptimizer:
    """获取平台内容优化器的单例"""
    global _optimizer_instance
    if _optimizer_instance is None:
        _optimizer_instance = PlatformContentOptimizer()
    return _optimizer_instance
