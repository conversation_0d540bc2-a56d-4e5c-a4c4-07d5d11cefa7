#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的项目文件清理工具
保护用户重要备份文件，只清理确认无用的文件
"""

import os
import shutil
import time
from pathlib import Path
from typing import List, Dict, Set

def get_protected_files() -> List[str]:
    """获取受保护的文件列表（不会被删除）"""
    return [
        # 用户备份文件
        "project_backup_*.zip",
        "backup_*.zip", 
        "*.backup",
        
        # 重要配置文件
        "config/app_settings.json",
        "config/llm_config.json",
        "config/tts_config.json",
        "config/video_generation_config.py",
        
        # 核心程序文件
        "main.py",
        "requirements.txt",
        
        # 用户数据
        "user_data/**/*",
        "output/**/*.mp4",
        "output/**/*.json",
        
        # 需求规范
        ".kiro/**/*"
    ]

def get_safe_cleanup_files() -> Dict[str, List[str]]:
    """获取安全清理的文件列表（确认无用的文件）"""
    return {
        # 1. 临时缓存目录（可安全删除）
        "temp_cache_dirs": [
            "selenium",
            "selenium_chrome_data", 
            "cache/video_analysis",
            "temp/image_cache",
            "temp/test_videos", 
            "temp/uploaded_images",
            "temp/vheer_videos",
            "temp/video_cache",
            "temp/video_conversion"
        ],
        
        # 2. 日志文件（可安全删除）
        "log_files": [
            "chromedriver.log",
            "startup_error.log",
            "logs/error.log",
            "logs/system.log"
        ],
        
        # 3. 已确认的重复测试文件（可安全删除）
        "confirmed_duplicate_tests": [
            # 只包含确认重复的测试文件
        ],
        
        # 4. 浏览器临时文件（可安全删除）
        "browser_temp": [
            "chromedriver.exe",  # 如果有新版本
        ],
        
        # 5. Python缓存（可安全删除）
        "python_cache": [
            "**/__pycache__",
            "**/*.pyc",
            "**/*.pyo"
        ]
    }

def is_protected_file(file_path: str, protected_patterns: List[str]) -> bool:
    """检查文件是否受保护"""
    import fnmatch
    
    for pattern in protected_patterns:
        if fnmatch.fnmatch(file_path, pattern):
            return True
        if fnmatch.fnmatch(str(Path(file_path).name), pattern):
            return True
    return False

def safe_cleanup_files(files_to_cleanup: Dict[str, List[str]], protected_files: List[str], dry_run: bool = True):
    """安全清理文件"""
    total_deleted = 0
    total_size_saved = 0
    protected_count = 0
    
    print(f"🛡️ 受保护的文件模式: {len(protected_files)} 个")
    
    for category, file_list in files_to_cleanup.items():
        print(f"\n🗂️ 清理类别: {category}")
        print("-" * 50)
        
        category_deleted = 0
        category_size = 0
        category_protected = 0
        
        for file_path in file_list:
            path = Path(file_path)
            
            # 检查是否受保护
            if is_protected_file(file_path, protected_files):
                print(f"   🛡️ 受保护跳过: {file_path}")
                category_protected += 1
                continue
            
            try:
                if path.exists():
                    # 计算文件/目录大小
                    if path.is_file():
                        size = path.stat().st_size
                    else:
                        size = sum(f.stat().st_size for f in path.rglob('*') if f.is_file())
                    
                    size_mb = size / (1024 * 1024)
                    
                    if dry_run:
                        print(f"   🔍 将删除: {file_path} ({size_mb:.2f} MB)")
                    else:
                        if path.is_file():
                            path.unlink()
                        else:
                            shutil.rmtree(path)
                        print(f"   ✅ 已删除: {file_path} ({size_mb:.2f} MB)")
                        
                    category_deleted += 1
                    category_size += size
                else:
                    print(f"   ⚠️ 不存在: {file_path}")
                    
            except Exception as e:
                print(f"   ❌ 删除失败: {file_path} - {e}")
        
        total_deleted += category_deleted
        total_size_saved += category_size
        protected_count += category_protected
        
        print(f"   📊 {category}: 删除 {category_deleted} 个, 保护 {category_protected} 个, {category_size/(1024*1024):.2f} MB")
    
    print(f"\n📊 安全清理总结:")
    print(f"   - 删除: {total_deleted} 个文件/目录")
    print(f"   - 保护: {protected_count} 个文件/目录")
    print(f"   - 节省空间: {total_size_saved/(1024*1024):.2f} MB")
    
    return total_deleted, total_size_saved, protected_count

def create_new_backup():
    """创建新的项目备份"""
    print("\n💾 是否创建新的项目备份？")
    choice = input("创建备份？(y/n): ").strip().lower()
    
    if choice == 'y':
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        backup_name = f"project_backup_{timestamp}.zip"
        
        try:
            import zipfile
            
            print(f"📦 创建备份: {backup_name}")
            
            with zipfile.ZipFile(backup_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 备份重要目录
                important_dirs = ['src', 'config', '.kiro', 'doc']
                important_files = ['main.py', 'requirements.txt']
                
                for dir_name in important_dirs:
                    if os.path.exists(dir_name):
                        for root, dirs, files in os.walk(dir_name):
                            for file in files:
                                file_path = os.path.join(root, file)
                                zipf.write(file_path)
                
                for file_name in important_files:
                    if os.path.exists(file_name):
                        zipf.write(file_name)
            
            backup_size = os.path.getsize(backup_name) / (1024 * 1024)
            print(f"✅ 备份创建成功: {backup_name} ({backup_size:.2f} MB)")
            
        except Exception as e:
            print(f"❌ 备份创建失败: {e}")

def main():
    """主函数"""
    print("🛡️ 安全项目文件清理工具")
    print("=" * 50)
    print("⚠️ 此版本会保护所有用户备份文件")
    
    # 获取受保护文件和清理文件
    protected_files = get_protected_files()
    files_to_cleanup = get_safe_cleanup_files()
    
    # 显示清理预览
    print("📋 安全清理预览:")
    total_files = sum(len(files) for files in files_to_cleanup.values())
    print(f"   - 将检查 {len(files_to_cleanup)} 个类别")
    print(f"   - 涉及约 {total_files} 个文件/目录")
    print(f"   - 受保护模式: {len(protected_files)} 个")
    
    # 询问用户确认
    print("\n选择操作:")
    print("   1. 预览模式 (查看将删除什么)")
    print("   2. 安全清理 (只删除确认无用的文件)")
    print("   3. 创建新备份")
    print("   4. 取消")
    
    choice = input("\n请选择 (1/2/3/4): ").strip()
    
    if choice == "1":
        print("\n🔍 预览模式 - 不会实际删除文件")
        deleted_count, size_saved, protected_count = safe_cleanup_files(
            files_to_cleanup, protected_files, dry_run=True
        )
        
    elif choice == "2":
        print("\n🛡️ 安全清理模式 - 保护重要文件")
        confirm = input("确认执行安全清理？(yes/no): ").strip().lower()
        
        if confirm == "yes":
            deleted_count, size_saved, protected_count = safe_cleanup_files(
                files_to_cleanup, protected_files, dry_run=False
            )
            
            print("\n🎉 安全清理完成！")
            print("💡 所有重要文件已受到保护")
        else:
            print("❌ 清理已取消")
            return
            
    elif choice == "3":
        create_new_backup()
        return
        
    else:
        print("❌ 操作已取消")
        return
    
    if choice in ["1", "2"]:
        print(f"\n📊 最终统计:")
        print(f"   - 处理文件: {deleted_count} 个")
        print(f"   - 保护文件: {protected_count} 个")
        print(f"   - 节省空间: {size_saved/(1024*1024):.2f} MB")

if __name__ == "__main__":
    main()
