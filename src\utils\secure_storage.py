# -*- coding: utf-8 -*-
"""
安全存储模块
负责敏感数据（如Cookie、API密钥）的加密和解密。
"""

import os
from pathlib import Path
from cryptography.fernet import Fernet

from src.utils.logger import logger

class SecureStorage:
    """使用Fernet对称加密来安全地存储和检索数据"""
    _instance = None
    _key_path = Path("user_data/secure.key")

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, '_initialized'): return
        self._initialized = True
        self.key = self._load_or_generate_key()
        self.fernet = Fernet(self.key)
        logger.info("安全存储模块初始化完成")

    def _load_or_generate_key(self) -> bytes:
        """加载或生成加密密钥"""
        try:
            self._key_path.parent.mkdir(parents=True, exist_ok=True)
            if self._key_path.exists():
                logger.info(f"从 {self._key_path} 加载加密密钥")
                return self._key_path.read_bytes()
            else:
                logger.info(f"加密密钥不存在，生成新的密钥到 {self._key_path}")
                key = Fernet.generate_key()
                self._key_path.write_bytes(key)
                # 在某些系统上，设置文件权限以增加安全性
                try:
                    os.chmod(self._key_path, 0o600)
                except OSError as e:
                    logger.warning(f"无法设置密钥文件权限: {e}")
                return key
        except Exception as e:
            logger.error(f"加载或生成密钥时发生严重错误: {e}")
            raise

    def encrypt(self, data: str) -> bytes:
        """将字符串数据加密"""
        try:
            return self.fernet.encrypt(data.encode('utf-8'))
        except Exception as e:
            logger.error(f"加密数据失败: {e}")
            raise

    def decrypt(self, encrypted_data: bytes) -> str:
        """解密数据到字符串"""
        try:
            decrypted_bytes = self.fernet.decrypt(encrypted_data)
            return decrypted_bytes.decode('utf-8')
        except Exception as e:
            # 可能是密钥已更改或数据已损坏
            logger.error(f"解密数据失败: {e}")
            raise

# --- 单例访问 ---
_secure_storage_instance = None

def get_secure_storage() -> SecureStorage:
    """获取安全存储模块的单例"""
    global _secure_storage_instance
    if _secure_storage_instance is None:
        _secure_storage_instance = SecureStorage()
    return _secure_storage_instance
