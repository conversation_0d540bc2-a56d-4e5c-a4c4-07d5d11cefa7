# 五阶段分镜系统使用指南 🎬

五阶段分镜系统是AI视频生成器的核心功能，通过五个递进的阶段，将原始文本转换为详细的分镜脚本。

## 📋 系统概览

### 五个阶段
1. **阶段1: 世界观构建** - 分析故事背景和设定
2. **阶段2: 角色管理** - 提取和管理角色信息
3. **阶段3: 场景分析** - 分析场景结构和关系
4. **阶段4: 分镜生成** - 生成详细的分镜脚本
5. **阶段5: 优化建议** - 提供专业的优化建议

### 工作流程
```
原始文本 → 阶段1 → 阶段2 → 阶段3 → 阶段4 → 阶段5 → 分镜脚本
```

## 🎯 详细使用指南

### 阶段1: 世界观构建

#### 功能说明
- 分析文本的故事背景
- 确定时代、地点、风格
- 建立整体视觉风格

#### 操作步骤
1. 在"📝 文本处理"标签页输入原始文本
2. 选择合适的LLM模型
3. 切换到"🎬 五阶段分镜"标签页
4. 点击"生成阶段1: 世界观"按钮
5. 等待AI分析并生成世界观描述

#### 输出内容
- 故事背景设定
- 时代和地点信息
- 整体视觉风格描述
- 氛围和色调建议

### 阶段2: 角色管理

#### 功能说明
- 自动提取文本中的角色
- 分析角色特征和关系
- 建立角色数据库

#### 操作步骤
1. 确保阶段1已完成
2. 点击"生成阶段2: 角色管理"按钮
3. 系统自动分析角色信息
4. 在角色列表中查看提取的角色
5. 可手动编辑角色信息

#### 输出内容
- 角色列表和基本信息
- 角色外观描述
- 角色性格特征
- 角色关系图谱

### 阶段3: 场景分析

#### 功能说明
- 将文本分割为不同场景
- 分析场景的环境和氛围
- 确定场景转换关系

#### 操作步骤
1. 确保前两个阶段已完成
2. 点击"生成阶段3: 场景分析"按钮
3. 系统自动分析场景结构
4. 在场景列表中查看分析结果
5. 可调整场景划分

#### 输出内容
- 场景列表和描述
- 场景环境设定
- 场景氛围分析
- 场景转换逻辑

### 阶段4: 分镜生成

#### 功能说明
- 基于前三个阶段生成详细分镜
- 包含镜头描述、角色动作、对话等
- 生成可用于图像生成的描述

#### 操作步骤
1. 确保前三个阶段已完成
2. 点击"生成阶段4: 分镜生成"按钮
3. 系统生成详细分镜脚本
4. 在分镜列表中查看每个镜头
5. 可编辑和调整分镜内容

#### 输出内容
- 详细分镜脚本
- 镜头描述和构图
- 角色动作和表情
- 对话和旁白
- 技术参数建议

### 阶段5: 优化建议

#### 功能说明
- 分析整体分镜质量
- 提供专业优化建议
- 检查逻辑连贯性

#### 操作步骤
1. 确保前四个阶段已完成
2. 点击"生成阶段5: 优化建议"按钮
3. 系统分析分镜质量
4. 查看优化建议列表
5. 根据建议调整分镜

#### 输出内容
- 整体质量评估
- 具体优化建议
- 逻辑问题提醒
- 技术改进方案

## 💡 使用技巧

### 提高质量
- **文本准备**: 确保原始文本清晰、完整
- **逐步完善**: 按顺序完成各阶段，不要跳跃
- **手动调整**: 在AI生成基础上进行人工优化
- **多次迭代**: 可以重新生成某个阶段来改进结果

### 常见问题
- **角色识别不准确**: 在阶段2手动编辑角色信息
- **场景划分不合理**: 在阶段3调整场景分割
- **分镜描述不够详细**: 在阶段4补充具体描述
- **逻辑不连贯**: 根据阶段5的建议进行调整

### 最佳实践
1. **准备阶段**: 整理好完整的故事文本
2. **设置阶段**: 选择合适的LLM模型和参数
3. **生成阶段**: 按顺序完成五个阶段
4. **优化阶段**: 根据建议进行人工调整
5. **验证阶段**: 检查整体效果和逻辑

## 🔗 相关功能

- [图像生成](图像生成使用指南.md) - 基于分镜生成图像
- [一致性控制](一致性控制使用指南.md) - 保持角色和场景一致性
- [项目管理](项目管理指南.md) - 保存和管理分镜项目

---

**提示**: 五阶段分镜系统是循序渐进的，建议按顺序完成各阶段以获得最佳效果。
