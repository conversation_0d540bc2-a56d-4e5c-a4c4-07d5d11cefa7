# 图像生成使用指南 🖼️

AI视频生成器支持多种图像生成引擎，可以将分镜脚本转换为高质量的视觉图像。

## 🎨 支持的图像生成引擎

### Pollinations AI (推荐)
- **类型**: 免费在线服务
- **特点**: 无需配置，开箱即用
- **模型**: flux, flux-turbo, gptimage等
- **优势**: 快速生成，多种风格
- **适用**: 快速原型和测试

### ComfyUI (本地)
- **类型**: 本地部署服务
- **特点**: 高质量定制生成
- **模型**: 支持各种Stable Diffusion模型
- **优势**: 完全控制，无网络依赖
- **适用**: 专业制作和定制需求

### Stability AI (商用)
- **类型**: 付费API服务
- **特点**: 专业级图像质量
- **模型**: SDXL, SD3等先进模型
- **优势**: 商用授权，高质量
- **适用**: 商业项目和高端制作

## 🚀 快速开始

### 基础设置
1. 打开"🖼️ 分镜图像生成"标签页
2. 在"引擎设置"中选择图像生成引擎
3. 配置相关参数（尺寸、质量等）
4. 确保有可用的分镜数据

### 单张生成
1. 在分镜列表中选择要生成的镜头
2. 点击"生成图像"按钮
3. 等待生成完成
4. 在预览区域查看结果

### 批量生成
1. 点击"批量生成所有图像"按钮
2. 系统自动为所有分镜生成图像
3. 可以跳过已生成的图像
4. 实时查看生成进度

## ⚙️ 详细配置

### Pollinations AI 配置

#### 基本参数
- **模型选择**: flux (推荐), flux-turbo, gptimage
- **图像尺寸**: 1024x1024, 1280x720, 1920x1080
- **种子值**: 随机或固定种子
- **增强模式**: 启用AI增强功能
- **水印设置**: 隐藏Pollinations标识

#### 高级参数
- **提示词增强**: 自动优化描述文本
- **风格一致性**: 保持整体风格统一
- **质量控制**: 设置生成质量等级
- **重试机制**: 失败时自动重试

### ComfyUI 配置

#### 服务器设置
- **服务器地址**: 默认 http://127.0.0.1:8188
- **连接测试**: 检查服务器状态
- **工作流选择**: 选择预设工作流
- **模型管理**: 管理本地模型文件

#### 工作流参数
- **采样器**: 选择合适的采样算法
- **步数设置**: 控制生成质量和速度
- **CFG Scale**: 调整提示词遵循度
- **分辨率**: 设置输出图像尺寸

### Stability AI 配置

#### API设置
- **API密钥**: 配置Stability AI密钥
- **模型选择**: SDXL, SD3等模型
- **服务器区域**: 选择最近的服务器
- **配额管理**: 监控API使用量

#### 生成参数
- **风格预设**: 选择预定义风格
- **质量等级**: 设置生成质量
- **创意程度**: 控制创意发挥度
- **安全过滤**: 内容安全检查

## 🎯 使用技巧

### 提高生成质量

#### 优化提示词
- **具体描述**: 使用详细、具体的描述
- **风格关键词**: 添加风格和氛围词汇
- **技术参数**: 包含构图、光线等技术要求
- **避免冲突**: 避免矛盾的描述内容

#### 参数调优
- **尺寸选择**: 根据用途选择合适尺寸
- **种子管理**: 使用固定种子保持一致性
- **质量平衡**: 在质量和速度间找到平衡
- **批量设置**: 批量生成时使用统一参数

### 一致性控制

#### 角色一致性
- 使用一致性控制面板管理角色
- 为每个角色建立标准描述
- 在生成时引用角色描述
- 定期检查和调整角色设定

#### 场景一致性
- 建立场景风格指南
- 使用统一的色调和氛围
- 保持光线和构图风格
- 注意场景间的逻辑连贯

### 批量处理

#### 效率优化
- **预设模板**: 创建常用参数模板
- **批量队列**: 合理安排生成队列
- **资源管理**: 监控系统资源使用
- **错误处理**: 设置失败重试机制

#### 质量控制
- **分阶段检查**: 分批检查生成质量
- **快速预览**: 使用低质量快速预览
- **重点优化**: 对关键镜头重点优化
- **版本管理**: 保留多个版本供选择

## 🔧 故障排除

### 常见问题

#### 生成失败
- **网络问题**: 检查网络连接状态
- **服务器问题**: 确认服务器正常运行
- **参数错误**: 检查参数设置是否正确
- **配额限制**: 确认API配额充足

#### 质量问题
- **描述不准确**: 优化提示词描述
- **风格不一致**: 使用一致性控制功能
- **细节缺失**: 增加描述细节和技术参数
- **构图问题**: 调整构图相关参数

#### 性能问题
- **生成速度慢**: 选择更快的引擎或参数
- **内存不足**: 降低图像尺寸或批量大小
- **服务器超载**: 错峰使用或更换服务器
- **网络延迟**: 选择更近的服务器节点

## 🔗 相关功能

- [五阶段分镜系统](五阶段分镜系统使用指南.md) - 生成分镜脚本
- [一致性控制](一致性控制使用指南.md) - 保持视觉一致性
- [视频生成](视频生成使用指南.md) - 将图像转换为视频

---

**提示**: 建议先从Pollinations AI开始，熟悉功能后再尝试其他引擎。
