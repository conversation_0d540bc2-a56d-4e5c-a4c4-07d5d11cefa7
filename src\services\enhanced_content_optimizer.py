# -*- coding: utf-8 -*-
"""
增强的内容AI优化服务
在现有基础上添加更多高级功能，包括封面生成建议、多语言支持、情感分析等
"""

import asyncio
import json
import re
import os
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

from .content_optimizer import ContentOptimizer, OptimizedContent
from src.core.service_manager import ServiceManager, ServiceType
from src.utils.logger import logger

@dataclass
class EnhancedOptimizedContent(OptimizedContent):
    """增强的优化内容"""
    cover_suggestions: List[str] = None  # 封面建议
    sentiment_analysis: Dict[str, Any] = None  # 情感分析
    trending_score: float = 0.0  # 热门度评分
    engagement_prediction: Dict[str, float] = None  # 互动预测
    multilingual_versions: Dict[str, Dict[str, str]] = None  # 多语言版本

@dataclass
class ContentAnalysis:
    """内容分析结果"""
    sentiment: str  # positive, negative, neutral
    emotion: str  # joy, sadness, anger, fear, surprise, disgust
    topics: List[str]  # 主题标签
    complexity_level: str  # simple, medium, complex
    target_age_group: str  # children, teens, adults, seniors
    content_category: str  # entertainment, education, news, lifestyle, etc.

class EnhancedContentOptimizer(ContentOptimizer):
    """增强的内容AI优化服务"""
    
    def __init__(self, llm_service=None):
        super().__init__(llm_service)
        
        # 扩展平台配置
        self.platform_configs.update({
            'tiktok': {
                'title_max_length': 150,
                'description_max_length': 2200,
                'max_tags': 10,
                'trending_topics': ['fyp', 'viral', 'trending', 'challenge', 'dance', 'comedy'],
                'style': '年轻化、潮流、国际化',
                'language': 'en'
            },
            'instagram_reels': {
                'title_max_length': 125,
                'description_max_length': 2200,
                'max_tags': 30,
                'trending_topics': ['reels', 'instagram', 'viral', 'trending', 'lifestyle'],
                'style': '视觉化、时尚、生活方式',
                'language': 'en'
            },
            'facebook': {
                'title_max_length': 255,
                'description_max_length': 63206,
                'max_tags': 10,
                'trending_topics': ['facebook', 'social', 'community', 'share', 'connect'],
                'style': '社交化、分享、连接',
                'language': 'en'
            }
        })
        
        # 情感分析关键词
        self.emotion_keywords = {
            'joy': ['开心', '快乐', '高兴', '兴奋', '愉快', 'happy', 'joy', 'excited'],
            'sadness': ['伤心', '难过', '悲伤', '失落', 'sad', 'sorrow', 'grief'],
            'anger': ['愤怒', '生气', '恼火', '愤慨', 'angry', 'mad', 'furious'],
            'fear': ['害怕', '恐惧', '担心', '焦虑', 'fear', 'scared', 'worried'],
            'surprise': ['惊讶', '震惊', '意外', '惊奇', 'surprise', 'shocked', 'amazed'],
            'disgust': ['厌恶', '恶心', '反感', 'disgust', 'gross', 'yuck']
        }
        
        # 内容类别关键词
        self.category_keywords = {
            'entertainment': ['娱乐', '搞笑', '有趣', '好玩', 'funny', 'entertainment', 'comedy'],
            'education': ['教育', '学习', '知识', '教程', 'education', 'learning', 'tutorial'],
            'lifestyle': ['生活', '日常', '分享', '体验', 'lifestyle', 'daily', 'life'],
            'technology': ['科技', '技术', '数码', '创新', 'technology', 'tech', 'innovation'],
            'food': ['美食', '料理', '烹饪', '食物', 'food', 'cooking', 'recipe'],
            'travel': ['旅行', '旅游', '探索', '风景', 'travel', 'trip', 'explore'],
            'fitness': ['健身', '运动', '锻炼', '健康', 'fitness', 'workout', 'health'],
            'beauty': ['美妆', '护肤', '时尚', '穿搭', 'beauty', 'makeup', 'fashion']
        }
        
        logger.info("增强内容AI优化服务初始化完成")
        
    async def enhanced_optimize_content(self, 
                                      original_title: str = "",
                                      original_description: str = "",
                                      video_content_summary: str = "",
                                      target_platforms: List[str] = None,
                                      target_audience: str = "大众",
                                      content_type: str = "娱乐",
                                      enable_advanced_features: bool = True) -> EnhancedOptimizedContent:
        """
        增强的内容优化
        
        Args:
            original_title: 原始标题
            original_description: 原始描述
            video_content_summary: 视频内容摘要
            target_platforms: 目标平台列表
            target_audience: 目标受众
            content_type: 内容类型
            enable_advanced_features: 是否启用高级功能
        """
        try:
            logger.info("开始增强AI内容优化...")
            
            # 首先进行基础优化
            base_result = await self.optimize_content(
                original_title, original_description, video_content_summary,
                target_platforms, target_audience, content_type
            )
            
            # 创建增强结果对象
            enhanced_result = EnhancedOptimizedContent(
                title=base_result.title,
                description=base_result.description,
                tags=base_result.tags,
                hashtags=base_result.hashtags,
                keywords=base_result.keywords,
                platform_specific=base_result.platform_specific
            )
            
            if enable_advanced_features:
                # 内容分析
                content_analysis = await self._analyze_content(
                    original_title, original_description, video_content_summary
                )
                
                # 生成封面建议
                enhanced_result.cover_suggestions = await self._generate_cover_suggestions(
                    enhanced_result.title, enhanced_result.description, content_analysis
                )
                
                # 情感分析
                enhanced_result.sentiment_analysis = await self._perform_sentiment_analysis(
                    enhanced_result.title, enhanced_result.description, content_analysis
                )
                
                # 热门度评分
                enhanced_result.trending_score = await self._calculate_trending_score(
                    enhanced_result.title, enhanced_result.tags, target_platforms or []
                )
                
                # 互动预测
                enhanced_result.engagement_prediction = await self._predict_engagement(
                    enhanced_result, target_platforms or []
                )
                
                # 多语言版本（如果需要）
                if any(platform in ['tiktok', 'youtube', 'instagram_reels'] for platform in (target_platforms or [])):
                    enhanced_result.multilingual_versions = await self._generate_multilingual_versions(
                        enhanced_result.title, enhanced_result.description
                    )
            
            logger.info("增强AI内容优化完成")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"增强内容优化失败: {e}")
            # 返回基础优化结果
            return EnhancedOptimizedContent(
                title=original_title or "精彩视频",
                description=original_description or "分享精彩内容",
                tags=["视频", "分享"],
                hashtags=["#视频", "#分享"],
                keywords=["视频", "内容"],
                platform_specific={}
            )
            
    async def _analyze_content(self, title: str, description: str, summary: str) -> ContentAnalysis:
        """分析内容特征"""
        try:
            # 合并所有文本
            full_text = f"{title} {description} {summary}".lower()
            
            # 情感分析
            sentiment = self._detect_sentiment(full_text)
            emotion = self._detect_emotion(full_text)
            
            # 主题检测
            topics = self._detect_topics(full_text)
            
            # 复杂度分析
            complexity = self._analyze_complexity(full_text)
            
            # 目标年龄组
            age_group = self._detect_age_group(full_text)
            
            # 内容类别
            category = self._detect_category(full_text)
            
            return ContentAnalysis(
                sentiment=sentiment,
                emotion=emotion,
                topics=topics,
                complexity_level=complexity,
                target_age_group=age_group,
                content_category=category
            )
            
        except Exception as e:
            logger.error(f"内容分析失败: {e}")
            return ContentAnalysis(
                sentiment="neutral",
                emotion="joy",
                topics=["通用"],
                complexity_level="medium",
                target_age_group="adults",
                content_category="entertainment"
            )
            
    def _detect_sentiment(self, text: str) -> str:
        """检测情感倾向"""
        positive_words = ['好', '棒', '赞', '优秀', '精彩', 'amazing', 'great', 'awesome', 'excellent']
        negative_words = ['差', '糟', '坏', '失望', '难过', 'bad', 'terrible', 'awful', 'disappointing']
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"
            
    def _detect_emotion(self, text: str) -> str:
        """检测主要情感"""
        emotion_scores = {}
        
        for emotion, keywords in self.emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text)
            emotion_scores[emotion] = score
            
        # 返回得分最高的情感，如果都为0则返回joy
        if max(emotion_scores.values()) == 0:
            return "joy"
        
        return max(emotion_scores, key=emotion_scores.get)
        
    def _detect_topics(self, text: str) -> List[str]:
        """检测主题标签"""
        topics = []
        
        # 基于关键词检测主题
        topic_keywords = {
            '科技': ['科技', '技术', '数码', '创新', 'tech', 'technology'],
            '生活': ['生活', '日常', '分享', 'life', 'daily'],
            '美食': ['美食', '料理', '烹饪', 'food', 'cooking'],
            '旅行': ['旅行', '旅游', '风景', 'travel', 'trip'],
            '娱乐': ['娱乐', '搞笑', '有趣', 'entertainment', 'funny'],
            '教育': ['教育', '学习', '知识', 'education', 'learning']
        }
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in text for keyword in keywords):
                topics.append(topic)
                
        return topics if topics else ['通用']
        
    def _analyze_complexity(self, text: str) -> str:
        """分析内容复杂度"""
        # 简单的复杂度分析：基于文本长度和词汇复杂度
        if len(text) < 50:
            return "simple"
        elif len(text) < 200:
            return "medium"
        else:
            return "complex"
            
    def _detect_age_group(self, text: str) -> str:
        """检测目标年龄组"""
        age_keywords = {
            'children': ['儿童', '小朋友', '孩子', 'kids', 'children'],
            'teens': ['青少年', '学生', '年轻', 'teen', 'student', 'young'],
            'adults': ['成人', '工作', '职场', 'adult', 'work', 'professional'],
            'seniors': ['老人', '长辈', '退休', 'senior', 'elderly']
        }
        
        for age_group, keywords in age_keywords.items():
            if any(keyword in text for keyword in keywords):
                return age_group
                
        return "adults"  # 默认成人
        
    def _detect_category(self, text: str) -> str:
        """检测内容类别"""
        category_scores = {}
        
        for category, keywords in self.category_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text)
            category_scores[category] = score
            
        if max(category_scores.values()) == 0:
            return "entertainment"
            
        return max(category_scores, key=category_scores.get)
        
    async def _generate_cover_suggestions(self, title: str, description: str, 
                                        analysis: ContentAnalysis) -> List[str]:
        """生成封面建议"""
        try:
            prompt = f"""
基于以下视频信息，生成5个吸引人的封面设计建议：

标题：{title}
描述：{description}
内容类别：{analysis.content_category}
情感：{analysis.emotion}
主题：{', '.join(analysis.topics)}

请生成封面设计建议，包括：
1. 主要视觉元素
2. 色彩搭配
3. 文字设计
4. 构图建议

每个建议用一行描述，格式：[视觉元素] + [色彩] + [文字] + [构图]
"""

            if self.llm_service:
                result = await self.llm_service.generate_text(prompt, max_tokens=600, temperature=0.7)
                if result.success:
                    response = result.data.get('content', '')
                    suggestions = [line.strip() for line in response.split('\n') if line.strip()]
                    return suggestions[:5]
                    
            # 备选方案
            return [
                f"明亮色彩 + {analysis.emotion}表情 + 大字标题 + 居中构图",
                f"对比色彩 + 动作场景 + 简洁文字 + 三分构图",
                f"温暖色调 + 人物特写 + 标题突出 + 黄金分割",
                f"冷色调 + 场景全景 + 小字说明 + 对称构图",
                f"高对比 + 关键元素 + 无文字 + 动态构图"
            ]
            
        except Exception as e:
            logger.error(f"生成封面建议失败: {e}")
            return ["使用明亮色彩和清晰标题的简洁设计"]
            
    async def _perform_sentiment_analysis(self, title: str, description: str, 
                                        analysis: ContentAnalysis) -> Dict[str, Any]:
        """执行情感分析"""
        return {
            'overall_sentiment': analysis.sentiment,
            'primary_emotion': analysis.emotion,
            'confidence_score': 0.8,  # 模拟置信度
            'emotional_keywords': self._extract_emotional_keywords(f"{title} {description}"),
            'tone': self._analyze_tone(f"{title} {description}")
        }
        
    def _extract_emotional_keywords(self, text: str) -> List[str]:
        """提取情感关键词"""
        emotional_words = []
        text_lower = text.lower()
        
        for emotion, keywords in self.emotion_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    emotional_words.append(keyword)
                    
        return list(set(emotional_words))
        
    def _analyze_tone(self, text: str) -> str:
        """分析语调"""
        formal_indicators = ['请', '您', '敬请', 'please', 'kindly']
        casual_indicators = ['哈哈', '嘿', '哇', 'haha', 'wow', 'hey']
        
        formal_count = sum(1 for indicator in formal_indicators if indicator in text.lower())
        casual_count = sum(1 for indicator in casual_indicators if indicator in text.lower())
        
        if formal_count > casual_count:
            return "formal"
        elif casual_count > formal_count:
            return "casual"
        else:
            return "neutral"
            
    async def _calculate_trending_score(self, title: str, tags: List[str], 
                                      platforms: List[str]) -> float:
        """计算热门度评分"""
        try:
            # 基于标题和标签的热门关键词匹配
            trending_keywords = [
                '热门', '爆火', '病毒式', '必看', '震惊', 'viral', 'trending', 'hot', 'popular', 'must-watch'
            ]
            
            score = 0.0
            text = f"{title} {' '.join(tags)}".lower()
            
            # 关键词匹配加分
            for keyword in trending_keywords:
                if keyword in text:
                    score += 0.1
                    
            # 平台特定加分
            for platform in platforms:
                config = self.platform_configs.get(platform, {})
                trending_topics = config.get('trending_topics', [])
                for topic in trending_topics:
                    if topic.lower() in text:
                        score += 0.05
                        
            # 标签数量加分
            score += min(len(tags) * 0.02, 0.2)
            
            # 标题长度适中加分
            if 10 <= len(title) <= 50:
                score += 0.1
                
            return min(score, 1.0)  # 最大值为1.0
            
        except Exception as e:
            logger.error(f"计算热门度评分失败: {e}")
            return 0.5
            
    async def _predict_engagement(self, content: EnhancedOptimizedContent, 
                                platforms: List[str]) -> Dict[str, float]:
        """预测互动率"""
        try:
            # 基于内容特征预测互动率
            base_engagement = 0.05  # 基础互动率5%
            
            # 标题吸引力加成
            if any(word in content.title.lower() for word in ['震惊', '必看', '不敢相信', 'amazing', 'incredible']):
                base_engagement += 0.02
                
            # 标签数量影响
            if 5 <= len(content.tags) <= 10:
                base_engagement += 0.01
                
            # 描述长度影响
            if 50 <= len(content.description) <= 200:
                base_engagement += 0.01
                
            # 平台特定调整
            platform_engagement = {}
            for platform in platforms:
                platform_multiplier = {
                    'douyin': 1.2,
                    'kuaishou': 1.1,
                    'xiaohongshu': 0.9,
                    'bilibili': 0.8,
                    'tiktok': 1.3,
                    'youtube': 0.7
                }.get(platform, 1.0)
                
                platform_engagement[platform] = min(base_engagement * platform_multiplier, 0.15)
                
            return platform_engagement
            
        except Exception as e:
            logger.error(f"预测互动率失败: {e}")
            return {platform: 0.05 for platform in platforms}
            
    async def _generate_multilingual_versions(self, title: str, 
                                            description: str) -> Dict[str, Dict[str, str]]:
        """生成多语言版本"""
        try:
            languages = {
                'en': 'English',
                'ja': 'Japanese',
                'ko': 'Korean',
                'es': 'Spanish'
            }
            
            multilingual_versions = {}
            
            for lang_code, lang_name in languages.items():
                if self.llm_service:
                    prompt = f"""
请将以下中文内容翻译成{lang_name}，保持原意和吸引力：

标题：{title}
描述：{description}

要求：
- 保持原文的情感和语调
- 适合社交媒体平台
- 简洁有力

请以JSON格式返回：
{{
    "title": "翻译后的标题",
    "description": "翻译后的描述"
}}
"""
                    
                    try:
                        result = await self.llm_service.generate_text(prompt, max_tokens=500, temperature=0.3)
                        if result.success:
                            response = result.data.get('content', '')
                            translation = json.loads(response)
                            multilingual_versions[lang_code] = {
                                'title': translation.get('title', title),
                                'description': translation.get('description', description)
                            }
                        else:
                            # 备选方案
                            multilingual_versions[lang_code] = {
                                'title': f"[{lang_name}] {title}",
                                'description': f"[{lang_name}] {description}"
                            }
                    except:
                        multilingual_versions[lang_code] = {
                            'title': title,
                            'description': description
                        }
                        
                # 避免API调用过于频繁
                await asyncio.sleep(0.5)
                
            return multilingual_versions
            
        except Exception as e:
            logger.error(f"生成多语言版本失败: {e}")
            return {}
            
    async def generate_content_series(self, base_content: Dict[str, str], 
                                    series_count: int = 5) -> List[Dict[str, str]]:
        """生成系列内容"""
        try:
            prompt = f"""
基于以下基础内容，生成{series_count}个相关的系列内容：

基础标题：{base_content.get('title', '')}
基础描述：{base_content.get('description', '')}

要求：
- 每个内容都要有独特性
- 保持主题相关性
- 适合制作成系列
- 标题要有吸引力

请为每个内容生成标题和描述，以JSON数组格式返回：
[
    {{"title": "系列1标题", "description": "系列1描述"}},
    {{"title": "系列2标题", "description": "系列2描述"}},
    ...
]
"""

            if self.llm_service:
                result = await self.llm_service.generate_text(prompt, max_tokens=1000, temperature=0.8)
                if result.success:
                    response = result.data.get('content', '')
                    try:
                        series_content = json.loads(response)
                        return series_content[:series_count]
                    except json.JSONDecodeError:
                        pass
                        
            # 备选方案
            base_title = base_content.get('title', '精彩内容')
            return [
                {'title': f"{base_title} - 第{i+1}集", 'description': f"系列内容第{i+1}部分"}
                for i in range(series_count)
            ]
            
        except Exception as e:
            logger.error(f"生成系列内容失败: {e}")
            return []
            
    async def optimize_for_accessibility(self, content: Dict[str, str]) -> Dict[str, str]:
        """为无障碍访问优化内容"""
        try:
            prompt = f"""
请为以下内容添加无障碍访问支持：

标题：{content.get('title', '')}
描述：{content.get('description', '')}

请添加：
1. 图像描述文本
2. 字幕建议
3. 语音描述要点

以JSON格式返回：
{{
    "alt_text": "图像描述文本",
    "subtitle_suggestions": "字幕建议",
    "audio_description": "语音描述要点"
}}
"""

            if self.llm_service:
                result = await self.llm_service.generate_text(prompt, max_tokens=600, temperature=0.5)
                if result.success:
                    response = result.data.get('content', '')
                    try:
                        accessibility_content = json.loads(response)
                        return accessibility_content
                    except json.JSONDecodeError:
                        pass
                        
            # 备选方案
            return {
                'alt_text': f"视频内容：{content.get('title', '精彩视频')}",
                'subtitle_suggestions': "建议添加清晰的字幕以提高可访问性",
                'audio_description': "建议添加音频描述以帮助视觉障碍用户"
            }
            
        except Exception as e:
            logger.error(f"无障碍优化失败: {e}")
            return {}