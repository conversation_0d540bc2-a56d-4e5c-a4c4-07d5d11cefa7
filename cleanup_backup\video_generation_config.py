# -*- coding: utf-8 -*-
"""
视频生成服务配置示例
"""

def _get_zhipu_api_key():
    """动态获取智谱AI API密钥"""
    try:
        from src.utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        models = config_manager.get_models()
        for model in models:
            if model.get('type') == 'zhipu' or model.get('name') == '智谱AI':
                api_key = model.get('key', '')
                if api_key:
                    return api_key
        return ''
    except Exception:
        # 如果获取失败，返回空字符串，让引擎自己处理
        return ''

# 默认配置
DEFAULT_CONFIG = {
    # 输出目录
    'output_dir': 'output/videos',
    
    # 路由策略: 'priority', 'load_balance', 'cost_optimize', 'free_first', 'fastest'
    'routing_strategy': 'free_first',
    
    # 引擎偏好: 'free', 'quality', 'speed', 'local'
    'engine_preferences': ['free', 'quality'],
    
    # 并发限制
    'concurrent_limit': 3,
    
    # 各引擎配置
    'engines': {
        # CogVideoX-Flash (智谱AI免费)
        'cogvideox_flash': {
            'enabled': True,
            'api_key': _get_zhipu_api_key(),  # 自动从配置管理器获取智谱AI密钥
            'base_url': 'https://open.bigmodel.cn/api/paas/v4',
            'model': 'cogvideox-flash',
            'timeout': 900,  # 增加到15分钟超时，给服务器更多时间
            'max_retries': 8,  # 增加重试次数到8次
            'max_duration': 10.0,  # 最大10秒
            'retry_delay': 30,  # 重试间隔30秒，避免频繁请求
            'max_concurrent': 3,  # 默认并发数，用户可以调整
            'supported_resolutions': [
                '720x480', '1024x1024', '1280x960',
                '960x1280', '1920x1080', '1080x1920',
                '2048x1080', '3840x2160'
            ],
            'supported_fps': [30, 60],  # 移除不支持的24fps
            'cost_per_second': 0.0  # 免费
        },

        # 豆包视频生成 (Doubao Seedance Pro)
        'doubao_seedance_pro': {
            'enabled': False,  # 默认禁用，需要用户配置API密钥
            'api_key': '0d5ead96-f0f9-4f0f-90b7-b76a743d6bd6',  # 用户提供的API密钥
            'base_url': 'https://ark.cn-beijing.volces.com/api/v3',  # 豆包API基础URL
            'model': 'doubao-seedance-1-0-pro-250528',  # 官方文档中的正确模型名称格式
            'timeout': 600,  # 10分钟超时
            'max_retries': 3,
            'max_duration': 10.0,  # 豆包支持5秒和10秒
            'retry_delay': 30,
            'max_concurrent': 10,  # 豆包支持10并发
            'supported_resolutions': [
                '480p', '1080p'  # 豆包官方支持的分辨率（移除720p）
            ],
            'supported_ratios': [
                'adaptive'  # 豆包只支持adaptive比例
            ],
            'supported_durations': [5, 10],  # 豆包支持的时长
            'cost_per_second': 0.02,  # 付费服务，估算成本
            'rpm_limit': 600,  # RPM限制
            'fps': 24,  # 固定24fps
            'cost_per_million_tokens': 15.0,  # 每百万token 15元
            'estimated_tokens_per_second': 50000,  # 估算每秒视频消耗token数

        },

        # 豆包视频生成 Lite版 (Doubao Seedance Lite) - 更便宜的选择
        'doubao_seedance_lite': {
            'enabled': True,  # 启用豆包Lite引擎
            'api_key': '0d5ead96-f0f9-4f0f-90b7-b76a743d6bd6',  # 用户提供的API密钥
            'base_url': 'https://ark.cn-beijing.volces.com/api/v3',  # 豆包API基础URL
            'model': 'doubao-seedance-1-0-lite-i2v-250428',  # Lite版模型正确ID
            'timeout': 600,  # 10分钟超时
            'max_retries': 3,
            'max_duration': 10.0,  # 豆包支持5秒和10秒
            'retry_delay': 30,
            'max_concurrent': 5,  # 豆包Lite支持5并发
            'supported_resolutions': [
                '480p', '720p', '1080p'  # Lite版支持更多分辨率
            ],
            'supported_ratios': [
                'adaptive'  # 豆包只支持adaptive比例
            ],
            'supported_durations': [5, 10],  # 豆包支持的时长
            'cost_per_second': 0.013,  # Lite版更便宜，估算成本
            'rpm_limit': 600,  # RPM限制
            'fps': 24,  # 固定24fps
            'cost_per_million_tokens': 10.0,  # 每百万token 10元 (比pro便宜33%)
            'estimated_tokens_per_second': 50000,  # 估算每秒视频消耗token数
        },

        # Replicate Stable Video Diffusion
        'replicate_svd': {
            'enabled': False,
            'api_key': '',  # 需要配置Replicate API密钥
            'base_url': 'https://api.replicate.com/v1',
            'model': 'stability-ai/stable-video-diffusion:3f0457e4619daac51203dedb1a4919c746077d07b83b0d6fcfff9b3b7956a6e',
            'timeout': 600,  # 10分钟超时
            'max_retries': 2,
            'max_duration': 4.0,  # 最大4秒
            'cost_per_second': 0.0125  # 约$0.0125/秒
        },
        
        # PixVerse AI
        'pixverse': {
            'enabled': False,
            'api_key': '',  # 需要配置PixVerse API密钥
            'base_url': 'https://api.pixverse.ai/v1',
            'timeout': 300,
            'max_retries': 2,
            'max_duration': 4.0,
            'cost_per_second': 0.0  # 有免费额度
        },
        
        # Haiper AI
        'haiper': {
            'enabled': False,
            'api_key': '',  # 需要配置Haiper API密钥
            'base_url': 'https://api.haiper.ai/v1',
            'timeout': 300,
            'max_retries': 2,
            'max_duration': 6.0,
            'cost_per_second': 0.0  # 有免费额度
        },
        
        # Runway ML
        'runway_ml': {
            'enabled': False,
            'api_key': '',  # 需要配置Runway API密钥
            'base_url': 'https://api.runwayml.com/v1',
            'timeout': 600,
            'max_retries': 2,
            'max_duration': 16.0,
            'cost_per_second': 0.05  # 约$0.05/秒
        },
        
        # Pika Labs
        'pika_labs': {
            'enabled': False,
            'api_key': '',  # 需要配置Pika API密钥
            'base_url': 'https://api.pika.art/v1',
            'timeout': 300,
            'max_retries': 2,
            'max_duration': 3.0,
            'cost_per_second': 0.02  # 约$0.02/秒
        },

        # Vheer.com 图生视频 (免费，基于浏览器自动化)
        'vheer': {
            'enabled': True,  # 默认启用
            'headless': True,  # 无头模式运行
            'output_dir': 'output/videos/vheer',
            'max_wait_time': 300,  # 5分钟超时
            'max_concurrent': 1,  # 单并发，避免被网站限制
            'timeout': 300,
            'max_retries': 2,
            'retry_delay': 30,  # 重试间隔30秒
            'max_duration': 10.0,  # 估算最大时长
            'cost_per_second': 0.0,  # 免费
            'supported_formats': ['jpg', 'jpeg', 'png', 'webp'],
            'output_format': 'mp4',
            'description': 'Vheer.com免费图生视频服务，基于浏览器自动化技术'
        }
    }
}

# 开发环境配置（主要使用免费服务）
DEVELOPMENT_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'free_first',
    'engine_preferences': ['free'],
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'cogvideox_flash': {
            **DEFAULT_CONFIG['engines']['cogvideox_flash'],
            'enabled': True
        },
        'doubao_seedance_pro': {
            **DEFAULT_CONFIG['engines']['doubao_seedance_pro'],
            'enabled': False  # 开发环境默认禁用付费服务
        },
        'doubao_seedance_lite': {
            **DEFAULT_CONFIG['engines']['doubao_seedance_lite'],
            'enabled': False  # 开发环境默认禁用付费服务
        },
        'vheer': {
            **DEFAULT_CONFIG['engines']['vheer'],
            'enabled': True  # 开发环境启用免费的Vheer服务
        },
        'replicate_svd': {'enabled': False},
        'runway_ml': {'enabled': False},
        'pika_labs': {'enabled': False}
    }
}

# 生产环境配置（包含付费服务作为备选）
PRODUCTION_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'priority',
    'engine_preferences': ['free', 'quality'],
    'concurrent_limit': 3,
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'cogvideox_flash': {
            **DEFAULT_CONFIG['engines']['cogvideox_flash'],
            'enabled': True,
            'api_key': _get_zhipu_api_key()  # 自动从配置管理器获取智谱AI密钥
        },
        'doubao_seedance_pro': {
            **DEFAULT_CONFIG['engines']['doubao_seedance_pro'],
            'enabled': True  # 生产环境可启用豆包
        },
        'doubao_seedance_lite': {
            **DEFAULT_CONFIG['engines']['doubao_seedance_lite'],
            'enabled': True  # 生产环境可启用豆包Lite版
        },
        'replicate_svd': {
            **DEFAULT_CONFIG['engines']['replicate_svd'],
            'enabled': True,
            'api_key': 'your-replicate-api-key'  # 需要实际配置
        }
    }
}

# 高质量配置（优先使用高质量引擎）
HIGH_QUALITY_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'best_quality',
    'engine_preferences': ['quality', 'free'],
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'runway_ml': {
            **DEFAULT_CONFIG['engines']['runway_ml'],
            'enabled': True,
            'api_key': 'your-runway-api-key'  # 需要实际配置
        },
        'cogvideox_flash': {
            **DEFAULT_CONFIG['engines']['cogvideox_flash'],
            'enabled': True
        }
    }
}

# 成本优化配置（优先使用免费/低成本引擎）
COST_OPTIMIZED_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'cheapest',
    'engine_preferences': ['free'],
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'cogvideox_flash': {
            **DEFAULT_CONFIG['engines']['cogvideox_flash'],
            'enabled': True
        },
        'pixverse': {
            **DEFAULT_CONFIG['engines']['pixverse'],
            'enabled': True
        },
        'haiper': {
            **DEFAULT_CONFIG['engines']['haiper'],
            'enabled': True
        }
    }
}


def get_config(environment: str = 'development'):
    """根据环境获取配置
    
    Args:
        environment: 环境名称 ('development', 'production', 'high_quality', 'cost_optimized')
    
    Returns:
        配置字典
    """
    configs = {
        'development': DEVELOPMENT_CONFIG,
        'production': PRODUCTION_CONFIG,
        'high_quality': HIGH_QUALITY_CONFIG,
        'cost_optimized': COST_OPTIMIZED_CONFIG,
        'default': DEFAULT_CONFIG
    }
    
    return configs.get(environment, DEFAULT_CONFIG)


def get_engine_config(engine_name: str, environment: str = 'development'):
    """获取特定引擎的配置
    
    Args:
        engine_name: 引擎名称
        environment: 环境名称
    
    Returns:
        引擎配置字典
    """
    config = get_config(environment)
    return config.get('engines', {}).get(engine_name, {})


def is_engine_enabled(engine_name: str, environment: str = 'development') -> bool:
    """检查引擎是否启用
    
    Args:
        engine_name: 引擎名称
        environment: 环境名称
    
    Returns:
        是否启用
    """
    engine_config = get_engine_config(engine_name, environment)
    return engine_config.get('enabled', False)


def get_enabled_engines(environment: str = 'development') -> list:
    """获取启用的引擎列表
    
    Args:
        environment: 环境名称
    
    Returns:
        启用的引擎名称列表
    """
    config = get_config(environment)
    enabled_engines = []
    
    for engine_name, engine_config in config.get('engines', {}).items():
        if engine_config.get('enabled', False):
            enabled_engines.append(engine_name)
    
    return enabled_engines
