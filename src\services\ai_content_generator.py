# -*- coding: utf-8 -*-
"""
AI内容生成器
集成LLM服务，为不同平台生成定制化的视频标题、描述和标签。
"""

import time
import asyncio
from dataclasses import dataclass, field
from typing import List, Dict, Any

from src.utils.logger import logger
from src.core.service_manager import ServiceManager, ServiceType
from src.services.project_content_analyzer import ProjectContent


@dataclass
class GeneratedContent:
    """AI生成的内容模型"""
    titles: List[str] = field(default_factory=list)
    description: str = ""
    tags: List[str] = field(default_factory=list)
    platform: str = ""
    confidence_score: float = 0.0
    generation_time: float = 0.0


class AIContentGenerator:
    """
    集成现有LLM服务，生成平台特定的内容。
    """
    def __init__(self, llm_service=None):
        # 🔧 修复：使用统一的LLM服务
        if llm_service is None:
            try:
                # 从服务管理器获取LLM服务
                service_manager = ServiceManager()
                self.llm_service = service_manager.get_service(ServiceType.LLM)
                if not self.llm_service:
                    logger.error("LLM服务未找到，AI内容生成功能将不可用")
            except Exception as e:
                logger.error(f"获取LLM服务失败: {e}")
                self.llm_service = None
        else:
            self.llm_service = llm_service

        self.platform_templates = self._load_platform_templates()
        self.content_cache: Dict[str, GeneratedContent] = {}
        logger.info("AI内容生成器初始化完成")

    def _load_platform_templates(self) -> Dict[str, Dict[str, str]]:
        """加载或定义各平台的提示词模板"""
        return {
            "douyin": {
                "title_prompt": "为以下内容创作5个吸引人的抖音短视频标题，风格要活泼、有悬念、能引发好奇心。标题要简短，最好在15个字以内.\n\n内容摘要：{summary}",
                "desc_prompt": "为以下内容创作一段抖音视频描述，要包含表情符号，引导用户评论和点赞。\n\n内容摘要：{summary}",
                "tags_prompt": "为以下内容生成10个抖音热门标签。\n\n内容摘要：{summary}"
            },
            "bilibili": {
                "title_prompt": "为以下内容创作3个专业的B站视频标题，可以适当长一些，突出内容的深度和看点。\n\n内容摘要：{summary}",
                "desc_prompt": "为以下内容创作一段详细的B站视频简介，可以包含视频看点、相关链接和制作人员信息。\n\n内容摘要：{summary}",
                "tags_prompt": "为以下内容生成10个B站相关分区和主题的标签。\n\n内容摘要：{summary}"
            },
            "default": {
                "title_prompt": "为以下内容创作3个视频标题。\n\n内容摘要：{summary}",
                "desc_prompt": "为以下内容创作一段视频描述。\n\n内容摘要：{summary}",
                "tags_prompt": "为以下内容生成10个相关的标签。\n\n内容摘要：{summary}"
            }
        }

    async def generate_content(
        self, 
        project_context: ProjectContent, 
        platform: str
    ) -> GeneratedContent:
        """基于项目内容，为特定平台生成完整的内容（标题、描述、标签）"""
        cache_key = f"{project_context.title}|{project_context.summary}|{platform}"
        if cache_key in self.content_cache:
            logger.info(f"从缓存中获取平台 '{platform}' 的内容")
            return self.content_cache[cache_key]

        start_time = time.time()
        logger.info(f"开始为平台 '{platform}' 生成内容...")

        templates = self.platform_templates.get(platform.lower(), self.platform_templates['default'])

        # 🔧 修复：优先使用世界观圣经内容，然后是摘要，最后是原始文本
        context_summary = (project_context.world_building or
                          project_context.summary or
                          project_context.original_text)

        if not context_summary:
            logger.warning("项目内容为空，无法生成内容")
            return GeneratedContent(platform=platform)

        # 记录使用的内容类型
        if project_context.world_building:
            logger.info(f"使用世界观圣经内容生成{platform}平台内容，长度: {len(project_context.world_building)}")
        elif project_context.summary:
            logger.info(f"使用项目摘要生成{platform}平台内容，长度: {len(project_context.summary)}")
        else:
            logger.info(f"使用原始文本生成{platform}平台内容，长度: {len(project_context.original_text)}")

        try:
            if not self.llm_service:
                raise Exception("LLM服务不可用")

            # 🔧 修复：使用统一的LLM服务execute方法
            titles_task = self.llm_service.execute(
                prompt=templates['title_prompt'].format(summary=context_summary),
                max_tokens=500,
                temperature=0.8
            )
            desc_task = self.llm_service.execute(
                prompt=templates['desc_prompt'].format(summary=context_summary),
                max_tokens=800,
                temperature=0.7
            )
            tags_task = self.llm_service.execute(
                prompt=templates['tags_prompt'].format(summary=context_summary),
                max_tokens=300,
                temperature=0.6
            )

            titles_result, desc_result, tags_result = await asyncio.gather(
                titles_task, desc_task, tags_task
            )

            # 检查LLM调用是否成功并提取内容
            if not titles_result.success:
                raise Exception(f"标题生成失败: {titles_result.error}")
            if not desc_result.success:
                raise Exception(f"描述生成失败: {desc_result.error}")
            if not tags_result.success:
                raise Exception(f"标签生成失败: {tags_result.error}")

            generated_titles = titles_result.data.get('content', '')
            generated_desc = desc_result.data.get('content', '')
            generated_tags_str = tags_result.data.get('content', '')

            titles = [t.strip() for t in generated_titles.split('\n') if t.strip()]
            tags = [tag.strip().replace('#', '') for tag in generated_tags_str.split() if tag.strip()]

            generation_time = time.time() - start_time
            logger.info(f"平台 '{platform}' 内容生成完成，耗时 {generation_time:.2f} 秒")

            generated_content = GeneratedContent(
                titles=titles,
                description=generated_desc.strip(),
                tags=tags,
                platform=platform,
                confidence_score=0.9,
                generation_time=generation_time
            )
            self.content_cache[cache_key] = generated_content
            return generated_content

        except Exception as e:
            logger.error(f"为平台 '{platform}' 生成内容时出错: {e}")
            return GeneratedContent(platform=platform)


# --- 单例访问 --- #
_generator_instance = None

def get_ai_content_generator() -> AIContentGenerator:
    """获取AI内容生成器的单例"""
    global _generator_instance
    if _generator_instance is None:
        # 🔧 修复：直接创建，让构造函数自己获取LLM服务
        _generator_instance = AIContentGenerator()
    return _generator_instance
