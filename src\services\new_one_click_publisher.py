# -*- coding: utf-8 -*-
"""
全新的一键发布系统
简单、稳定、实用的视频发布解决方案
"""

import os
import time
import json
import asyncio
import concurrent.futures
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict, field
from enum import Enum
from abc import ABC, abstractmethod # 新增导入abstractmethod

from src.utils.logger import logger


class PublishStatus(Enum):
    """发布状态枚举"""
    PENDING = "pending"          # 等待中
    PREPARING = "preparing"      # 准备中
    UPLOADING = "uploading"      # 上传中
    PROCESSING = "processing"    # 处理中
    SUCCESS = "success"          # 成功
    FAILED = "failed"           # 失败
    CANCELLED = "cancelled"      # 已取消


@dataclass
class VideoInfo:
    """视频信息"""
    file_path: str
    title: str
    description: str = ""
    tags: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class PublishTask:
    """发布任务"""
    id: str
    video_info: VideoInfo
    target_platforms: List[str]
    status: PublishStatus = PublishStatus.PENDING
    progress: float = 0.0
    message: str = ""
    results: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['status'] = self.status.value
        return data


class PlatformPublisher(ABC):
    """平台发布器基类 (抽象)"""
    
    def __init__(self, platform_name: str):
        self.platform_name = platform_name
        self.is_ready = False
    
    @abstractmethod
    async def prepare(self) -> bool:
        """准备发布环境"""
        raise NotImplementedError
    
    @abstractmethod
    async def publish(self, video_info: VideoInfo, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """发布视频"""
        raise NotImplementedError

    @abstractmethod
    async def cleanup(self):
        """清理资源"""
        raise NotImplementedError


class NewOneClickPublisher:
    """新的一键发布器 - 支持并发"""
    
    def __init__(self):
        self.publishers: Dict[str, PlatformPublisher] = {}
        self.current_task: Optional[PublishTask] = None
        self.is_publishing = False
        self.supported_platforms = {
            'douyin': '抖音',
            'kuaishou': '快手',
            'xiaohongshu': '小红书',
            'bilibili': 'B站'
        }
        logger.info("新一键发布器(并发版)初始化完成")
    
    def get_supported_platforms(self) -> Dict[str, str]:
        return self.supported_platforms.copy()
    
    def register_publisher(self, platform: str, publisher: PlatformPublisher):
        self.publishers[platform] = publisher
        logger.info(f"注册{platform}发布器成功")
    
    def create_task(self, video_info: VideoInfo, target_platforms: List[str]) -> PublishTask:
        import uuid
        task_id = str(uuid.uuid4())
        task = PublishTask(id=task_id, video_info=video_info, target_platforms=target_platforms)
        logger.info(f"创建发布任务: {task_id}")
        return task
    
    def validate_task(self, task: PublishTask) -> List[str]:
        errors = []
        if not os.path.exists(task.video_info.file_path):
            errors.append(f"视频文件不存在: {task.video_info.file_path}")
        if not task.video_info.title.strip():
            errors.append("视频标题不能为空")
        for platform in task.target_platforms:
            if platform not in self.supported_platforms:
                errors.append(f"不支持的平台: {platform}")
        return errors

    async def publish_task(self, task: PublishTask, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """以并发方式执行发布任务"""
        if self.is_publishing:
            return {'success': False, 'error': '已有发布任务在进行中'}
        
        errors = self.validate_task(task)
        if errors:
            return {'success': False, 'error': '任务验证失败: ' + '; '.join(errors)}
        
        self.current_task = task
        self.is_publishing = True
        
        try:
            task.status = PublishStatus.PREPARING
            if progress_callback: progress_callback(0.0, "准备发布环境...")

            # 并发准备发布器
            prepared_publishers: Dict[str, PlatformPublisher] = {}
            
            async def prepare_publisher(platform):
                if platform in self.publishers:
                    publisher = self.publishers[platform]
                    if await publisher.prepare():
                        prepared_publishers[platform] = publisher
                    else:
                        task.results[platform] = {'success': False, 'error': f'{platform}发布器准备失败'}
                else:
                    task.results[platform] = {'success': False, 'error': f'{platform}发布器未注册'}
            
            await asyncio.gather(*(prepare_publisher(p) for p in task.target_platforms))

            if not prepared_publishers:
                task.status = PublishStatus.FAILED
                return {'success': False, 'error': '没有可用的发布器'}

            # 使用ThreadPoolExecutor进行并发发布
            task.status = PublishStatus.UPLOADING
            total_platforms = len(prepared_publishers)
            completed_count = 0
            
            # 线程安全的进度更新
            progress_lock = asyncio.Lock()
            platform_progress_map = {p: 0.0 for p in prepared_publishers.keys()}

            async def update_overall_progress(platform, progress, message):
                async with progress_lock:
                    platform_progress_map[platform] = progress
                    overall_progress = sum(platform_progress_map.values()) / total_platforms
                    task.progress = overall_progress
                    if progress_callback:
                        platform_name = self.supported_platforms.get(platform, platform)
                        progress_callback(overall_progress, f"{platform_name}: {message}")

            def run_publish(platform, publisher):
                # 每个线程需要自己的事件循环来运行async函数
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def progress_adapter(progress, message):
                    await update_overall_progress(platform, progress, message)
                
                result = loop.run_until_complete(publisher.publish(task.video_info, progress_adapter))
                loop.close()
                return platform, result

            with concurrent.futures.ThreadPoolExecutor(max_workers=total_platforms) as executor:
                future_to_platform = {executor.submit(run_publish, p, pub): p for p, pub in prepared_publishers.items()}
                
                for future in concurrent.futures.as_completed(future_to_platform):
                    platform, result = future.result()
                    task.results[platform] = result
                    await update_overall_progress(platform, 1.0, "完成")
            
            success_count = sum(1 for r in task.results.values() if r.get('success', False))
            task.status = PublishStatus.SUCCESS if success_count > 0 else PublishStatus.FAILED
            task.message = f"发布完成: {success_count}/{total_platforms} 个平台成功"
            task.progress = 1.0
            
            return {
                'success': success_count > 0,
                'message': task.message,
                'results': task.results
            }

        except Exception as e:
            logger.error(f"发布任务执行失败: {e}")
            task.status = PublishStatus.FAILED
            task.message = f"发布失败: {e}"
            return {'success': False, 'error': str(e)}
        finally:
            self.is_publishing = False
            # 清理逻辑可以移到BrowserManager的shutdown方法中

    def cancel_task(self):
        if self.current_task:
            self.current_task.status = PublishStatus.CANCELLED
        self.is_publishing = False
        logger.info("发布任务已取消")

# 全局实例
_new_publisher = None
def get_new_one_click_publisher() -> NewOneClickPublisher:
    global _new_publisher
    if _new_publisher is None:
        _new_publisher = NewOneClickPublisher()
    return _new_publisher
