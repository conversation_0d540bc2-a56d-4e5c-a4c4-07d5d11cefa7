# -*- coding: utf-8 -*-
"""
增强数据模型
为发布系统提供扩展的数据模型和结构
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum

from src.services.platform_publisher.base_publisher import VideoMetadata, PublishResult


class PublishPriority(Enum):
    """发布优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class PublishMode(Enum):
    """发布模式"""
    IMMEDIATE = "immediate"
    SCHEDULED = "scheduled"
    BATCH = "batch"


@dataclass
class EnhancedVideoMetadata(VideoMetadata):
    """增强的视频元数据"""
    # 扩展字段
    category: str = ""
    language: str = "zh-CN"
    duration: Optional[float] = None
    file_size: Optional[int] = None
    resolution: str = ""
    frame_rate: Optional[float] = None
    
    # AI优化相关
    ai_optimized: bool = False
    optimization_level: str = "standard"
    generated_tags: List[str] = field(default_factory=list)
    
    # 平台特定配置
    platform_specific: Dict[str, Dict[str, Any]] = field(default_factory=dict)


@dataclass
class PublishConfiguration:
    """发布配置"""
    # 基本配置
    auto_optimize_content: bool = True
    convert_video_format: bool = True
    retry_failed_uploads: bool = True
    max_retries: int = 3
    
    # 批量发布配置
    max_concurrent_tasks: int = 3
    batch_delay: int = 5  # 秒
    
    # 平台配置
    platform_configs: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # 通知配置
    notify_on_completion: bool = True
    notify_on_failure: bool = True


@dataclass
class PublishTemplate:
    """发布模板"""
    id: str
    name: str
    description: str
    metadata_template: EnhancedVideoMetadata
    target_platforms: List[str]
    configuration: PublishConfiguration
    
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    usage_count: int = 0
    tags: List[str] = field(default_factory=list)


@dataclass
class PublishSchedule:
    """发布计划"""
    id: str
    name: str
    template_id: str
    
    # 调度配置
    schedule_time: datetime
    repeat_pattern: Optional[str] = None  # daily, weekly, monthly
    max_executions: Optional[int] = None
    
    # 状态
    is_active: bool = True
    execution_count: int = 0
    last_execution: Optional[datetime] = None
    next_execution: Optional[datetime] = None
    
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class PublishStatistics:
    """发布统计"""
    total_tasks: int = 0
    successful_tasks: int = 0
    failed_tasks: int = 0
    cancelled_tasks: int = 0
    
    # 平台统计
    platform_stats: Dict[str, Dict[str, int]] = field(default_factory=dict)
    
    # 时间统计
    average_duration: float = 0.0
    total_duration: float = 0.0
    
    # 更新时间
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class PlatformAccount:
    """平台账号信息"""
    id: str
    platform: str
    username: str
    display_name: str
    
    # 认证信息
    is_authenticated: bool = False
    auth_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_expires_at: Optional[datetime] = None
    
    # 账号状态
    is_active: bool = True
    last_login: Optional[datetime] = None
    login_count: int = 0
    
    # 配置
    default_config: Dict[str, Any] = field(default_factory=dict)
    
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class PublishHistory:
    """发布历史记录"""
    id: str
    task_id: str
    video_path: str
    metadata: EnhancedVideoMetadata
    target_platforms: List[str]
    
    # 执行信息
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration: Optional[float] = None
    
    # 结果
    results: Dict[str, PublishResult] = field(default_factory=dict)
    overall_success: bool = False
    
    # 错误信息
    error_message: Optional[str] = None
    retry_count: int = 0


@dataclass
class SystemConfiguration:
    """系统配置"""
    # 性能配置
    max_concurrent_tasks: int = 5
    max_memory_usage: int = 80  # 百分比
    enable_performance_monitoring: bool = True
    
    # 日志配置
    log_level: str = "INFO"
    log_retention_days: int = 30
    enable_detailed_logging: bool = True
    
    # 缓存配置
    enable_caching: bool = True
    cache_size: int = 1000
    cache_ttl: int = 3600  # 秒
    
    # 网络配置
    request_timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 5
    
    # 安全配置
    encrypt_credentials: bool = True
    auto_logout_minutes: int = 60
    
    # 更新时间
    updated_at: datetime = field(default_factory=datetime.now)


# 工厂函数
def create_default_metadata() -> EnhancedVideoMetadata:
    """创建默认的视频元数据"""
    return EnhancedVideoMetadata(
        title="",
        description="",
        tags=[],
        cover_path="",
        language="zh-CN",
        ai_optimized=False,
        optimization_level="standard"
    )


def create_default_configuration() -> PublishConfiguration:
    """创建默认的发布配置"""
    return PublishConfiguration(
        auto_optimize_content=True,
        convert_video_format=True,
        retry_failed_uploads=True,
        max_retries=3,
        max_concurrent_tasks=3,
        batch_delay=5
    )


def create_publish_template(name: str, description: str, platforms: List[str]) -> PublishTemplate:
    """创建发布模板"""
    import uuid
    
    return PublishTemplate(
        id=str(uuid.uuid4()),
        name=name,
        description=description,
        metadata_template=create_default_metadata(),
        target_platforms=platforms,
        configuration=create_default_configuration()
    )


def create_platform_account(platform: str, username: str, display_name: str = None) -> PlatformAccount:
    """创建平台账号"""
    import uuid
    
    return PlatformAccount(
        id=str(uuid.uuid4()),
        platform=platform,
        username=username,
        display_name=display_name or username
    )


# 数据验证函数
def validate_metadata(metadata: EnhancedVideoMetadata) -> List[str]:
    """验证视频元数据"""
    errors = []
    
    if not metadata.title.strip():
        errors.append("标题不能为空")
    
    if len(metadata.title) > 100:
        errors.append("标题长度不能超过100个字符")
    
    if len(metadata.description) > 5000:
        errors.append("描述长度不能超过5000个字符")
    
    if len(metadata.tags) > 20:
        errors.append("标签数量不能超过20个")
    
    return errors


def validate_configuration(config: PublishConfiguration) -> List[str]:
    """验证发布配置"""
    errors = []
    
    if config.max_retries < 0 or config.max_retries > 10:
        errors.append("最大重试次数必须在0-10之间")
    
    if config.max_concurrent_tasks < 1 or config.max_concurrent_tasks > 10:
        errors.append("最大并发任务数必须在1-10之间")
    
    if config.batch_delay < 0 or config.batch_delay > 60:
        errors.append("批量延迟必须在0-60秒之间")
    
    return errors


# 数据转换函数
def metadata_to_dict(metadata: EnhancedVideoMetadata) -> Dict[str, Any]:
    """将元数据转换为字典"""
    return {
        'title': metadata.title,
        'description': metadata.description,
        'tags': metadata.tags,
        'cover_path': metadata.cover_path,
        'category': metadata.category,
        'language': metadata.language,
        'duration': metadata.duration,
        'file_size': metadata.file_size,
        'resolution': metadata.resolution,
        'frame_rate': metadata.frame_rate,
        'ai_optimized': metadata.ai_optimized,
        'optimization_level': metadata.optimization_level,
        'generated_tags': metadata.generated_tags,
        'platform_specific': metadata.platform_specific
    }


def dict_to_metadata(data: Dict[str, Any]) -> EnhancedVideoMetadata:
    """将字典转换为元数据"""
    return EnhancedVideoMetadata(
        title=data.get('title', ''),
        description=data.get('description', ''),
        tags=data.get('tags', []),
        cover_path=data.get('cover_path', ''),
        category=data.get('category', ''),
        language=data.get('language', 'zh-CN'),
        duration=data.get('duration'),
        file_size=data.get('file_size'),
        resolution=data.get('resolution', ''),
        frame_rate=data.get('frame_rate'),
        ai_optimized=data.get('ai_optimized', False),
        optimization_level=data.get('optimization_level', 'standard'),
        generated_tags=data.get('generated_tags', []),
        platform_specific=data.get('platform_specific', {})
    )
